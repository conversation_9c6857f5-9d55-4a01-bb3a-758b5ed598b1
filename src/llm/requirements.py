"""
Intent extraction using OpenAI Structured Outputs.

Converts natural language prompts into structured AnalysisIntent objects
using OpenAI's Structured Outputs feature for reliable JSON generation.
"""

import os
import time
from typing import Dict, Any, Optional
from dataclasses import dataclass

from openai import OpenAI
from pydantic import ValidationError
import structlog

from src.models import AnalysisIntent
from src.api.exceptions import LLMAPIError, ValidationError as CustomValidationError
from src.safety.prompt_injection import PromptInjectionDetector

logger = structlog.get_logger()

@dataclass
class IntentExtractionResult:
    """Result of intent extraction operation."""
    intent: AnalysisIntent
    processing_time_seconds: float
    confidence_score: float
    raw_response: str

class IntentExtractor:
    """Extract structured analysis intent from natural language."""
    
    def __init__(self, model: str = None, api_key: str = None, base_url: str = None):
        """
        Initialize intent extractor with OpenRouter.
        
        Args:
            model: OpenRouter model to use (defaults to environment variable)
            api_key: OpenRouter API key (defaults to environment variable)
            base_url: OpenRouter base URL (defaults to environment variable)
        """
        self.model = model or os.getenv("OPENROUTER_MODEL", "openai/gpt-4o-mini")
        
        # OpenRouter configuration
        api_key = api_key or os.getenv("OPENROUTER_API_KEY")
        base_url = base_url or os.getenv("OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1")
        
        # Initialize OpenAI client with OpenRouter configuration
        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url,
            default_headers={
                "HTTP-Referer": os.getenv("OPENROUTER_SITE_URL", "https://localhost:8000"),
                "X-Title": os.getenv("OPENROUTER_APP_NAME", "Industry-Competitor-Analysis")
            }
        )
        
        self.injection_detector = PromptInjectionDetector()
        
        # Get JSON Schema for AnalysisIntent
        self.intent_schema = AnalysisIntent.model_json_schema()
        
        logger.info("Intent extractor initialized with OpenRouter", 
                   model=self.model, 
                   base_url=base_url)
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for intent extraction."""
        return """You are a structured data extractor specializing in competitive analysis requirements. Your task is to convert natural language requests into structured JSON that conforms exactly to the provided schema.

CRITICAL INSTRUCTIONS:
- Output ONLY valid JSON that matches the schema exactly
- Use strict mode compliance - every field must match the schema type
- If information is unclear or missing, use empty arrays/strings rather than guessing
- Map user language to the provided enums when possible
- Be conservative with inferences - only include what is explicitly stated or clearly implied

ENUM MAPPING GUIDELINES:
- Map business terms to ProductType (saas, on_prem, hardware_plus_software, services)
- Identify MarketPosition from context (leader, challenger, niche, new_entrant, unknown)
- Classify competitors as CompetitorType (direct, indirect, substitute, status_quo)
- Determine PrimaryGoal from the user's main question or objective

FIELD PRIORITIZATION:
- Always populate required fields: scope.primary_goal, market.industry_name, company_context.company_name, outputs
- Include geographies only if explicitly mentioned
- Add competitors only if specifically named
- Keep feature_taxonomy concise - push detailed questions to secondary_questions"""
    
    def _get_user_prompt(self, prompt: str) -> str:
        """Get the user prompt template with the actual user input."""
        return f"""User Request:
{prompt}

EXTRACTION GUIDELINES:
1. SCOPE ANALYSIS:
   - Identify the primary goal from: market entry assessment, competitive benchmarking, feature analysis, pricing review, positioning analysis, GTM strategy, market sizing, regulatory risk scan
   - Extract secondary questions that aren't covered by the primary goal
   - Note any decision deadlines or urgency indicators

2. MARKET CONTEXT:
   - Extract industry name and any subsector mentions
   - Identify geographic scope (only if explicitly stated)
   - Look for market size references, buyer types, or segment information
   - Note any time range constraints (current, historical, forecast)

3. COMPANY CONTEXT:
   - Identify the requesting company name
   - Determine product type and deployment model if mentioned
   - Assess current market position from context clues
   - Extract any unique value proposition statements

4. COMPETITIVE LANDSCAPE:
   - List explicitly mentioned competitors
   - Classify competitor types based on relationship to user's company
   - Note any exclusions or discovery rules
   - Identify preferred evidence sources

5. ANALYSIS REQUIREMENTS:
   - Extract specific features or capabilities to analyze
   - Identify pricing focus areas
   - Note any compliance or regulatory requirements
   - Determine desired output format and depth

Remember: Be precise and conservative. Only include information that is clearly stated or strongly implied in the user's request."""
    
    def _sanitize_prompt(self, prompt: str) -> str:
        """Sanitize user prompt to prevent injection attacks."""
        # Check for potential injection attempts
        scan_result = self.injection_detector.scan_input(prompt)
        
        if not scan_result.is_safe:
            logger.warning("Potential prompt injection detected", threats=scan_result.threats_detected)
            # For now, we'll log and continue, but in production you might want to reject
        
        # Basic sanitization - remove potential injection patterns
        sanitized = prompt.strip()
        
        # Remove common injection patterns
        injection_patterns = [
            "ignore previous instructions",
            "system prompt override", 
            "act as different character",
            "forget everything above"
        ]
        
        for pattern in injection_patterns:
            sanitized = sanitized.replace(pattern.lower(), "[REDACTED]")
            sanitized = sanitized.replace(pattern.upper(), "[REDACTED]")
            sanitized = sanitized.replace(pattern.title(), "[REDACTED]")
        
        return sanitized
    
    async def extract_intent(self, prompt: str) -> IntentExtractionResult:
        """
        Extract structured intent from natural language prompt.
        
        Args:
            prompt: Natural language analysis request
            
        Returns:
            IntentExtractionResult with structured intent and metadata
            
        Raises:
            LLMAPIError: For OpenAI API failures
            CustomValidationError: For schema validation failures
        """
        start_time = time.time()
        
        try:
            # Sanitize input prompt
            sanitized_prompt = self._sanitize_prompt(prompt)
            
            logger.info("Starting intent extraction", 
                       original_length=len(prompt),
                       sanitized_length=len(sanitized_prompt))
            
            # Prepare structured output format
            response_format = {
                "type": "json_schema",
                "json_schema": {
                    "name": "analysis_intent_schema",
                    "schema": self.intent_schema,
                    "strict": True
                }
            }
            
            # Call OpenAI API
            completion = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self._get_system_prompt()},
                    {"role": "user", "content": self._get_user_prompt(sanitized_prompt)}
                ],
                response_format=response_format,
                temperature=0.1,  # Low temperature for consistent extraction
                max_tokens=4000
            )
            
            # Extract response content
            raw_response = completion.choices[0].message.content
            
            if not raw_response:
                raise LLMAPIError("Empty response from OpenAI API")
            
            logger.debug("Raw LLM response received", response_length=len(raw_response))
            
            # Parse and validate JSON response
            try:
                intent = AnalysisIntent.model_validate_json(raw_response)
            except ValidationError as e:
                logger.error("Intent validation failed", error=str(e), raw_response=raw_response[:500])
                raise CustomValidationError(f"Invalid intent structure: {str(e)}")
            
            processing_time = time.time() - start_time
            
            # Calculate confidence score based on completeness
            confidence_score = self._calculate_confidence_score(intent)
            
            logger.info("Intent extraction completed successfully",
                       processing_time=processing_time,
                       confidence_score=confidence_score,
                       industry=intent.market.industry_name,
                       primary_goal=intent.scope.primary_goal)
            
            return IntentExtractionResult(
                intent=intent,
                processing_time_seconds=processing_time,
                confidence_score=confidence_score,
                raw_response=raw_response
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error("Intent extraction failed", 
                        error=str(e), 
                        processing_time=processing_time)
            
            if "rate_limit" in str(e).lower():
                raise LLMAPIError(f"OpenRouter rate limit exceeded: {str(e)}")
            elif "api" in str(e).lower():
                raise LLMAPIError(f"OpenRouter API error: {str(e)}")
            else:
                raise LLMAPIError(f"Unexpected error during intent extraction: {str(e)}")
    
    def _calculate_confidence_score(self, intent: AnalysisIntent) -> float:
        """
        Calculate confidence score based on intent completeness.
        
        Args:
            intent: Extracted AnalysisIntent object
            
        Returns:
            Confidence score between 0.0 and 1.0
        """
        score = 0.0
        max_score = 0.0
        
        # Required fields (high weight)
        required_fields = [
            (intent.scope.primary_goal, 0.2),
            (intent.market.industry_name, 0.2),
            (intent.company_context.company_name, 0.1)
        ]
        
        for field_value, weight in required_fields:
            max_score += weight
            if field_value:
                score += weight
        
        # Optional but valuable fields (medium weight)
        optional_fields = [
            (intent.market.geographies, 0.1),
            (intent.competition and intent.competition.competitor_list_explicit, 0.15),
            (intent.capabilities and intent.capabilities.feature_taxonomy, 0.1),
            (intent.scope.secondary_questions, 0.05)
        ]
        
        for field_value, weight in optional_fields:
            max_score += weight
            if field_value:
                score += weight
        
        # Context fields (lower weight)
        context_fields = [
            (intent.company_context.product_type, 0.05),
            (intent.company_context.current_market_position, 0.05)
        ]
        
        for field_value, weight in context_fields:
            max_score += weight
            if field_value:
                score += weight
        
        return min(score / max_score, 1.0) if max_score > 0 else 0.0