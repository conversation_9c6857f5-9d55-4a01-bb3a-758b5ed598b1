"""
Custom middleware for the FastAPI application.
"""

import time
import uuid
from typing import Callable
from collections import defaultdict, deque
from datetime import datetime, timedelta

from fastapi import Request, Response, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
import structlog

logger = structlog.get_logger()

class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for structured request/response logging."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request with structured logging."""
        # Generate request ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # Log request
        start_time = time.time()
        logger.info(
            "Request started",
            request_id=request_id,
            method=request.method,
            path=request.url.path,
            query_params=str(request.query_params),
            client_ip=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent")
        )
        
        # Process request
        try:
            response = await call_next(request)
            processing_time = time.time() - start_time
            
            # Log response
            logger.info(
                "Request completed",
                request_id=request_id,
                status_code=response.status_code,
                processing_time_seconds=processing_time
            )
            
            # Add request ID to response headers
            response.headers["X-Request-ID"] = request_id
            
            return response
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(
                "Request failed",
                request_id=request_id,
                error=str(e),
                processing_time_seconds=processing_time
            )
            raise

class RateLimitMiddleware(BaseHTTPMiddleware):
    """Simple in-memory rate limiting middleware."""
    
    def __init__(self, app, calls_per_minute: int = 60):
        super().__init__(app)
        self.calls_per_minute = calls_per_minute
        self.requests = defaultdict(deque)
        
        # Different limits for different endpoints
        self.endpoint_limits = {
            "/intent/extract": 30,  # More restrictive for LLM calls
            "/analyze": 5,          # Very restrictive for full analysis
            "/screenshots": 20,     # Moderate for screenshot capture
            "/research": 10,        # Moderate for research calls
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Apply rate limiting based on client IP and endpoint."""
        client_ip = request.client.host if request.client else "unknown"
        endpoint = request.url.path
        
        # Get rate limit for this endpoint
        limit = self.endpoint_limits.get(endpoint, self.calls_per_minute)
        
        # Clean old requests (older than 1 minute)
        now = datetime.utcnow()
        cutoff = now - timedelta(minutes=1)
        
        key = f"{client_ip}:{endpoint}"
        request_times = self.requests[key]
        
        # Remove old requests
        while request_times and request_times[0] < cutoff:
            request_times.popleft()
        
        # Check if limit exceeded
        if len(request_times) >= limit:
            logger.warning(
                "Rate limit exceeded",
                client_ip=client_ip,
                endpoint=endpoint,
                current_requests=len(request_times),
                limit=limit
            )
            
            return JSONResponse(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                content={
                    "error": {
                        "message": "Rate limit exceeded",
                        "limit": limit,
                        "window_minutes": 1,
                        "retry_after": 60
                    }
                },
                headers={"Retry-After": "60"}
            )
        
        # Add current request
        request_times.append(now)
        
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers
        response.headers["X-RateLimit-Limit"] = str(limit)
        response.headers["X-RateLimit-Remaining"] = str(limit - len(request_times))
        response.headers["X-RateLimit-Reset"] = str(int((now + timedelta(minutes=1)).timestamp()))
        
        return response

class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Add security headers to all responses."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Add security headers to response."""
        response = await call_next(request)
        
        # Security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Content-Security-Policy"] = "default-src 'self'"
        
        # Remove server header
        if "server" in response.headers:
            del response.headers["server"]
        
        return response