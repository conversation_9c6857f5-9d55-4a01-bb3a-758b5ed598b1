"""
Intent extraction endpoints - convert natural language to structured analysis requirements.
"""

from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel, Field
import structlog

from src.models import AnalysisIntent
from src.llm.requirements import IntentExtractor
from src.api.exceptions import LLMA<PERSON><PERSON>rror, ValidationError

logger = structlog.get_logger()

router = APIRouter()

class ExtractRequest(BaseModel):
    """Request model for intent extraction."""
    prompt: str = Field(min_length=10, max_length=2000, description="Natural language analysis request")

class ExtractResponse(BaseModel):
    """Response model for intent extraction."""
    intent: AnalysisIntent
    processing_time_seconds: float
    confidence_score: float = Field(ge=0.0, le=1.0)

@router.post("/extract", response_model=ExtractResponse)
async def extract_intent(request: ExtractRequest) -> ExtractResponse:
    """
    Extract structured analysis intent from natural language prompt.
    
    Uses OpenAI Structured Outputs to convert free-text requests into
    validated AnalysisIntent objects that can drive the analysis pipeline.
    
    Args:
        request: Contains the natural language prompt to analyze
        
    Returns:
        ExtractResponse with structured intent and metadata
        
    Raises:
        HTTPException: For validation errors, API failures, or processing issues
    """
    logger.info("Intent extraction requested", prompt_length=len(request.prompt))
    
    try:
        # Initialize intent extractor
        extractor = IntentExtractor()
        
        # Extract structured intent
        result = await extractor.extract_intent(request.prompt)
        
        logger.info(
            "Intent extraction completed",
            industry=result.intent.market.industry_name,
            primary_goal=result.intent.scope.primary_goal,
            processing_time=result.processing_time_seconds
        )
        
        return ExtractResponse(
            intent=result.intent,
            processing_time_seconds=result.processing_time_seconds,
            confidence_score=result.confidence_score
        )
        
    except ValidationError as e:
        logger.error("Intent validation failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=f"Intent validation failed: {str(e)}"
        )
        
    except LLMAPIError as e:
        logger.error("LLM API error during intent extraction", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail=f"LLM API error: {str(e)}"
        )
        
    except Exception as e:
        logger.error("Unexpected error during intent extraction", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during intent extraction"
        )

@router.post("/validate", response_model=dict)
async def validate_intent(intent: AnalysisIntent) -> dict:
    """
    Validate an AnalysisIntent object for completeness and consistency.
    
    Performs additional business logic validation beyond Pydantic schema validation.
    
    Args:
        intent: AnalysisIntent object to validate
        
    Returns:
        Validation results with any warnings or suggestions
    """
    logger.info("Intent validation requested", industry=intent.market.industry_name)
    
    try:
        # TODO: Implement business logic validation
        # - Check for required fields based on primary goal
        # - Validate competitor lists against known entities
        # - Check geographic scope consistency
        # - Validate feature taxonomy completeness
        
        warnings = []
        suggestions = []
        
        # Example validations
        if not intent.competition or not intent.competition.competitor_list_explicit:
            warnings.append("No explicit competitors specified - analysis may be less targeted")
            suggestions.append("Consider adding specific competitor names for more focused analysis")
        
        if not intent.capabilities or not intent.capabilities.feature_taxonomy:
            warnings.append("No feature taxonomy specified - feature analysis may be generic")
            suggestions.append("Define specific features to analyze for more actionable insights")
        
        return {
            "valid": True,
            "warnings": warnings,
            "suggestions": suggestions,
            "completeness_score": 0.8  # TODO: Calculate based on filled fields
        }
        
    except Exception as e:
        logger.error("Error during intent validation", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during validation"
        )