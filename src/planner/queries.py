"""
Search query template generation for competitive research.

Generates optimized search queries for different search engines and
source discovery based on analysis intent and competitor information.
"""

from typing import List, Dict, Optional, Set
from pydantic import BaseModel, Field
from enum import Enum
import re

from src.models import AnalysisIntent, PrimaryGoal
from .sources import SourceType, SourceDiscovery

class SearchEngine(str, Enum):
    """Supported search engines."""
    GOOGLE = "google"
    BING = "bing"
    DUCKDUCKGO = "duckduckgo"

class QueryType(str, Enum):
    """Types of search queries."""
    COMPETITOR_DISCOVERY = "competitor_discovery"
    SOURCE_DISCOVERY = "source_discovery"
    FEATURE_RESEARCH = "feature_research"
    PRICING_RESEARCH = "pricing_research"
    MARKET_RESEARCH = "market_research"
    COMPARISON_RESEARCH = "comparison_research"

class SearchQuery(BaseModel):
    """A structured search query with metadata."""
    query: str = Field(description="The actual search query string")
    query_type: QueryType = Field(description="Type of query")
    source_type: Optional[SourceType] = Field(default=None, description="Target source type")
    competitor: Optional[str] = Field(default=None, description="Target competitor")
    search_engine: SearchEngine = Field(default=SearchEngine.GOOGLE, description="Preferred search engine")
    priority: int = Field(default=1, description="Query priority (1=highest, 5=lowest)")
    expected_results: int = Field(default=10, description="Expected number of results to process")
    filters: Dict[str, str] = Field(default_factory=dict, description="Additional search filters")

class QueryGenerator:
    """Generate optimized search queries for competitive research."""
    
    def __init__(self):
        """Initialize query generator."""
        self.source_discovery = SourceDiscovery()
        
        # Common search operators by engine
        self.search_operators = {
            SearchEngine.GOOGLE: {
                "site": "site:",
                "exact": '"{}"',
                "exclude": "-",
                "filetype": "filetype:",
                "intitle": "intitle:",
                "inurl": "inurl:",
                "daterange": "after:{} before:{}"
            },
            SearchEngine.BING: {
                "site": "site:",
                "exact": '"{}"',
                "exclude": "-",
                "filetype": "filetype:",
                "intitle": "intitle:",
                "inurl": "inurl:",
                "daterange": "after:{} before:{}"
            },
            SearchEngine.DUCKDUCKGO: {
                "site": "site:",
                "exact": '"{}"',
                "exclude": "-",
                "filetype": "filetype:",
                "intitle": "intitle:",
                "inurl": "inurl:",
                "daterange": ""  # Limited date support
            }
        }
    
    def generate_research_queries(self, intent: AnalysisIntent) -> List[SearchQuery]:
        """
        Generate comprehensive search queries based on analysis intent.
        
        Args:
            intent: Structured analysis intent
            
        Returns:
            List of prioritized search queries
        """
        queries = []
        
        # Generate competitor discovery queries
        queries.extend(self._generate_competitor_discovery_queries(intent))
        
        # Generate source discovery queries for known competitors
        if intent.competition and intent.competition.competitor_list_explicit:
            for competitor in intent.competition.competitor_list_explicit:
                queries.extend(self._generate_source_discovery_queries(intent, competitor))
        
        # Generate feature research queries
        if intent.capabilities and intent.capabilities.feature_taxonomy:
            queries.extend(self._generate_feature_research_queries(intent))
        
        # Generate pricing research queries
        if intent.pricing or "pricing" in intent.scope.primary_goal.value:
            queries.extend(self._generate_pricing_research_queries(intent))
        
        # Generate market research queries
        queries.extend(self._generate_market_research_queries(intent))
        
        # Sort by priority
        queries.sort(key=lambda q: q.priority)
        
        return queries
    
    def _generate_competitor_discovery_queries(self, intent: AnalysisIntent) -> List[SearchQuery]:
        """Generate queries to discover additional competitors."""
        queries = []
        industry = intent.market.industry_name
        
        # Base competitor discovery patterns
        discovery_patterns = [
            f"{industry} competitors",
            f"{industry} alternatives",
            f"{industry} vendors",
            f"{industry} solutions",
            f"best {industry} software",
            f"{industry} market leaders",
            f"{industry} comparison",
            f"top {industry} companies"
        ]
        
        # Add geographic qualifiers if specified
        if intent.market.geographies:
            geo_patterns = []
            for geo in intent.market.geographies:
                for pattern in discovery_patterns:
                    geo_patterns.append(f"{pattern} {geo}")
            discovery_patterns.extend(geo_patterns)
        
        # Add subsector specificity
        if intent.market.subsector:
            subsector_patterns = []
            for pattern in discovery_patterns:
                subsector_patterns.append(pattern.replace(industry, f"{intent.market.subsector} {industry}"))
            discovery_patterns.extend(subsector_patterns)
        
        # Create search queries
        for i, pattern in enumerate(discovery_patterns[:15]):  # Limit to top 15
            queries.append(SearchQuery(
                query=pattern,
                query_type=QueryType.COMPETITOR_DISCOVERY,
                priority=1 if i < 5 else 2,
                expected_results=20,
                search_engine=SearchEngine.GOOGLE
            ))
        
        return queries
    
    def _generate_source_discovery_queries(self, intent: AnalysisIntent, competitor: str) -> List[SearchQuery]:
        """Generate queries to discover sources for a specific competitor."""
        queries = []
        
        # Get source priorities based on analysis goal
        source_priorities = self.source_discovery.get_source_priorities(intent.scope.primary_goal.value)
        
        # Generate queries for each high-priority source type
        for source_type, priority in source_priorities.items():
            if priority.value in ["critical", "high"]:
                search_terms = self.source_discovery.get_search_terms_for_source(source_type, competitor)
                
                for term in search_terms[:3]:  # Limit to top 3 terms per source type
                    queries.append(SearchQuery(
                        query=term,
                        query_type=QueryType.SOURCE_DISCOVERY,
                        source_type=source_type,
                        competitor=competitor,
                        priority=1 if priority == "critical" else 2,
                        expected_results=10
                    ))
        
        return queries
    
    def _generate_feature_research_queries(self, intent: AnalysisIntent) -> List[SearchQuery]:
        """Generate queries for feature research."""
        queries = []
        
        if not intent.capabilities or not intent.capabilities.feature_taxonomy:
            return queries
        
        features = intent.capabilities.feature_taxonomy
        industry = intent.market.industry_name
        
        # Generate feature-specific queries
        for feature in features[:10]:  # Limit to top 10 features
            feature_queries = [
                f"{industry} {feature} comparison",
                f"{feature} {industry} software",
                f"best {feature} for {industry}",
                f"{industry} {feature} features"
            ]
            
            # Add competitor-specific feature queries
            if intent.competition and intent.competition.competitor_list_explicit:
                for competitor in intent.competition.competitor_list_explicit[:3]:  # Top 3 competitors
                    feature_queries.extend([
                        f"{competitor} {feature}",
                        f'"{competitor}" {feature} features'
                    ])
            
            for query in feature_queries:
                queries.append(SearchQuery(
                    query=query,
                    query_type=QueryType.FEATURE_RESEARCH,
                    priority=2,
                    expected_results=15
                ))
        
        return queries
    
    def _generate_pricing_research_queries(self, intent: AnalysisIntent) -> List[SearchQuery]:
        """Generate queries for pricing research."""
        queries = []
        industry = intent.market.industry_name
        
        # Base pricing queries
        pricing_patterns = [
            f"{industry} pricing comparison",
            f"{industry} cost comparison",
            f"{industry} software pricing",
            f"how much does {industry} software cost",
            f"{industry} pricing models"
        ]
        
        # Add competitor-specific pricing queries
        if intent.competition and intent.competition.competitor_list_explicit:
            for competitor in intent.competition.competitor_list_explicit:
                pricing_patterns.extend([
                    f"{competitor} pricing",
                    f"{competitor} cost",
                    f'"{competitor}" pricing plans',
                    f"site:{competitor.lower().replace(' ', '')}.com pricing"
                ])
        
        for pattern in pricing_patterns:
            queries.append(SearchQuery(
                query=pattern,
                query_type=QueryType.PRICING_RESEARCH,
                priority=1,
                expected_results=10
            ))
        
        return queries
    
    def _generate_market_research_queries(self, intent: AnalysisIntent) -> List[SearchQuery]:
        """Generate queries for market research."""
        queries = []
        industry = intent.market.industry_name
        
        # Market research patterns
        market_patterns = [
            f"{industry} market size",
            f"{industry} market analysis",
            f"{industry} industry report",
            f"{industry} market trends",
            f"{industry} growth rate"
        ]
        
        # Add geographic specificity
        if intent.market.geographies:
            for geo in intent.market.geographies:
                market_patterns.extend([
                    f"{industry} market {geo}",
                    f"{geo} {industry} industry"
                ])
        
        for pattern in market_patterns:
            queries.append(SearchQuery(
                query=pattern,
                query_type=QueryType.MARKET_RESEARCH,
                priority=3,
                expected_results=10
            ))
        
        return queries
    
    def optimize_query_for_engine(self, query: SearchQuery, engine: SearchEngine) -> str:
        """
        Optimize a query for a specific search engine.
        
        Args:
            query: Base search query
            engine: Target search engine
            
        Returns:
            Optimized query string
        """
        operators = self.search_operators.get(engine, {})
        optimized = query.query
        
        # Apply site restrictions if specified
        if query.competitor and query.source_type:
            competitor_domain = query.competitor.lower().replace(' ', '').replace('.', '')
            site_operator = operators.get("site", "site:")
            optimized = f"{site_operator}{competitor_domain}.com {optimized}"
        
        # Add filetype restrictions for specific source types
        if query.source_type == SourceType.WHITEPAPERS:
            filetype_operator = operators.get("filetype", "filetype:")
            optimized = f"{optimized} {filetype_operator}pdf"
        
        # Add URL restrictions for specific source types
        if query.source_type == SourceType.PRICING_PAGE:
            inurl_operator = operators.get("inurl", "inurl:")
            optimized = f"{optimized} {inurl_operator}pricing OR {inurl_operator}plans"
        
        return optimized
    
    def generate_comparison_queries(self, competitors: List[str], features: List[str] = None) -> List[SearchQuery]:
        """
        Generate queries for direct competitor comparisons.
        
        Args:
            competitors: List of competitors to compare
            features: Optional list of features to focus on
            
        Returns:
            List of comparison queries
        """
        queries = []
        
        # Generate pairwise comparison queries
        for i, comp1 in enumerate(competitors):
            for comp2 in competitors[i+1:]:
                comparison_queries = [
                    f"{comp1} vs {comp2}",
                    f'"{comp1}" vs "{comp2}"',
                    f"{comp1} {comp2} comparison",
                    f"compare {comp1} and {comp2}"
                ]
                
                # Add feature-specific comparisons
                if features:
                    for feature in features[:5]:  # Top 5 features
                        comparison_queries.append(f"{comp1} vs {comp2} {feature}")
                
                for query_text in comparison_queries:
                    queries.append(SearchQuery(
                        query=query_text,
                        query_type=QueryType.COMPARISON_RESEARCH,
                        priority=2,
                        expected_results=15
                    ))
        
        return queries
    
    def generate_review_platform_queries(self, competitors: List[str]) -> List[SearchQuery]:
        """
        Generate queries specifically for review platforms.
        
        Args:
            competitors: List of competitors
            
        Returns:
            List of review platform queries
        """
        queries = []
        review_sites = ["g2.com", "capterra.com", "trustradius.com", "softwareadvice.com"]
        
        for competitor in competitors:
            for site in review_sites:
                queries.append(SearchQuery(
                    query=f'site:{site} "{competitor}"',
                    query_type=QueryType.SOURCE_DISCOVERY,
                    source_type=SourceType.REVIEW_PLATFORMS,
                    competitor=competitor,
                    priority=2,
                    expected_results=5
                ))
        
        return queries
    
    def filter_duplicate_queries(self, queries: List[SearchQuery]) -> List[SearchQuery]:
        """
        Remove duplicate queries while preserving priority order.
        
        Args:
            queries: List of queries to deduplicate
            
        Returns:
            Deduplicated list of queries
        """
        seen_queries = set()
        filtered_queries = []
        
        for query in queries:
            # Create a normalized version for comparison
            normalized = query.query.lower().strip()
            
            if normalized not in seen_queries:
                seen_queries.add(normalized)
                filtered_queries.append(query)
        
        return filtered_queries
    
    def estimate_query_cost(self, queries: List[SearchQuery]) -> Dict[str, int]:
        """
        Estimate the cost/effort of executing queries.
        
        Args:
            queries: List of queries to estimate
            
        Returns:
            Dictionary with cost estimates
        """
        total_queries = len(queries)
        total_expected_results = sum(q.expected_results for q in queries)
        
        # Estimate by priority
        high_priority = len([q for q in queries if q.priority == 1])
        medium_priority = len([q for q in queries if q.priority == 2])
        low_priority = len([q for q in queries if q.priority >= 3])
        
        return {
            "total_queries": total_queries,
            "total_expected_results": total_expected_results,
            "high_priority_queries": high_priority,
            "medium_priority_queries": medium_priority,
            "low_priority_queries": low_priority,
            "estimated_api_calls": total_queries,
            "estimated_processing_time_minutes": total_queries * 2  # Rough estimate
        }