"""
Source type definitions and URL patterns for research planning.

Defines the types of sources to prioritize and patterns for discovering
relevant URLs for competitive analysis.
"""

from enum import Enum
from typing import List, Dict, Optional, Pattern
from pydantic import BaseModel, Field
import re
from dataclasses import dataclass

class SourceType(str, Enum):
    """Types of sources for competitive research."""
    VENDOR_SITE = "vendor_site"
    PRICING_PAGE = "pricing_page"
    DOCUMENTATION = "documentation"
    PRODUCT_PAGES = "product_pages"
    SECURITY_COMPLIANCE = "security_compliance"
    CASE_STUDIES = "case_studies"
    PRESS_RELEASES = "press_releases"
    REVIEW_PLATFORMS = "review_platforms"
    INDUSTRY_REPORTS = "industry_reports"
    SOCIAL_PROOF = "social_proof"
    TECHNICAL_SPECS = "technical_specs"
    CHANGELOG = "changelog"
    BLOG_POSTS = "blog_posts"
    WEBINARS = "webinars"
    WHITEPAPERS = "whitepapers"

class SourcePriority(str, Enum):
    """Priority levels for different source types."""
    CRITICAL = "critical"      # Must-have sources
    HIGH = "high"             # Important sources
    MEDIUM = "medium"         # Nice-to-have sources
    LOW = "low"              # Optional sources

@dataclass
class URLPattern:
    """URL pattern for discovering specific source types."""
    pattern: str
    description: str
    examples: List[str]
    
    def matches(self, url: str) -> bool:
        """Check if URL matches this pattern."""
        return bool(re.search(self.pattern, url, re.IGNORECASE))

class SourceDefinition(BaseModel):
    """Definition of a source type with discovery patterns."""
    source_type: SourceType
    priority: SourcePriority
    description: str
    url_patterns: List[URLPattern]
    search_terms: List[str] = Field(default_factory=list)
    typical_content: List[str] = Field(default_factory=list)
    
    class Config:
        arbitrary_types_allowed = True

class SourceDiscovery:
    """Discover and classify sources for competitive research."""
    
    def __init__(self):
        """Initialize source discovery with predefined patterns."""
        self.source_definitions = self._initialize_source_definitions()
    
    def _initialize_source_definitions(self) -> Dict[SourceType, SourceDefinition]:
        """Initialize source type definitions with URL patterns."""
        
        definitions = {
            SourceType.VENDOR_SITE: SourceDefinition(
                source_type=SourceType.VENDOR_SITE,
                priority=SourcePriority.CRITICAL,
                description="Official company website and main product pages",
                url_patterns=[
                    URLPattern(
                        pattern=r"^https?://(?:www\.)?[^/]+/?$",
                        description="Homepage",
                        examples=["https://benchling.com", "https://www.idbs.com"]
                    ),
                    URLPattern(
                        pattern=r"/(?:about|company|team)/?",
                        description="About pages",
                        examples=["https://benchling.com/about", "https://idbs.com/company"]
                    )
                ],
                search_terms=["official website", "company homepage"],
                typical_content=["company overview", "product descriptions", "value propositions"]
            ),
            
            SourceType.PRICING_PAGE: SourceDefinition(
                source_type=SourceType.PRICING_PAGE,
                priority=SourcePriority.CRITICAL,
                description="Pricing plans, tiers, and cost information",
                url_patterns=[
                    URLPattern(
                        pattern=r"/(?:pricing|plans|cost|subscribe|buy)/?",
                        description="Pricing pages",
                        examples=["https://benchling.com/pricing", "https://idbs.com/plans"]
                    ),
                    URLPattern(
                        pattern=r"/(?:enterprise|business|professional)/?.*(?:pricing|cost)",
                        description="Enterprise pricing",
                        examples=["https://company.com/enterprise-pricing"]
                    )
                ],
                search_terms=["pricing", "plans", "cost", "subscription"],
                typical_content=["pricing tiers", "feature comparisons", "billing cycles"]
            ),
            
            SourceType.DOCUMENTATION: SourceDefinition(
                source_type=SourceType.DOCUMENTATION,
                priority=SourcePriority.HIGH,
                description="Technical documentation and user guides",
                url_patterns=[
                    URLPattern(
                        pattern=r"/(?:docs|documentation|help|support|guide)/?",
                        description="Documentation sites",
                        examples=["https://docs.benchling.com", "https://help.idbs.com"]
                    ),
                    URLPattern(
                        pattern=r"\.(?:docs|help|support)\..*\.com",
                        description="Documentation subdomains",
                        examples=["https://docs.company.com", "https://help.company.com"]
                    )
                ],
                search_terms=["documentation", "user guide", "help center"],
                typical_content=["feature descriptions", "API references", "tutorials"]
            ),
            
            SourceType.SECURITY_COMPLIANCE: SourceDefinition(
                source_type=SourceType.SECURITY_COMPLIANCE,
                priority=SourcePriority.HIGH,
                description="Security features and compliance information",
                url_patterns=[
                    URLPattern(
                        pattern=r"/(?:security|compliance|privacy|gdpr|soc2|hipaa)/?",
                        description="Security and compliance pages",
                        examples=["https://benchling.com/security", "https://idbs.com/compliance"]
                    ),
                    URLPattern(
                        pattern=r"/(?:trust|certifications|audit)/?",
                        description="Trust and certification pages",
                        examples=["https://company.com/trust", "https://company.com/certifications"]
                    )
                ],
                search_terms=["security", "compliance", "certifications", "privacy"],
                typical_content=["security features", "compliance certifications", "data protection"]
            ),
            
            SourceType.PRODUCT_PAGES: SourceDefinition(
                source_type=SourceType.PRODUCT_PAGES,
                priority=SourcePriority.HIGH,
                description="Detailed product feature pages",
                url_patterns=[
                    URLPattern(
                        pattern=r"/(?:product|features|solutions|platform)/?",
                        description="Product feature pages",
                        examples=["https://benchling.com/features", "https://idbs.com/platform"]
                    ),
                    URLPattern(
                        pattern=r"/(?:capabilities|functionality|tools)/?",
                        description="Capability pages",
                        examples=["https://company.com/capabilities"]
                    )
                ],
                search_terms=["features", "capabilities", "product overview"],
                typical_content=["feature lists", "product screenshots", "use cases"]
            ),
            
            SourceType.CASE_STUDIES: SourceDefinition(
                source_type=SourceType.CASE_STUDIES,
                priority=SourcePriority.MEDIUM,
                description="Customer success stories and case studies",
                url_patterns=[
                    URLPattern(
                        pattern=r"/(?:case-studies|customers|success|stories)/?",
                        description="Case study pages",
                        examples=["https://benchling.com/case-studies", "https://idbs.com/customers"]
                    ),
                    URLPattern(
                        pattern=r"/(?:testimonials|reviews|references)/?",
                        description="Customer testimonials",
                        examples=["https://company.com/testimonials"]
                    )
                ],
                search_terms=["case studies", "customer success", "testimonials"],
                typical_content=["customer stories", "ROI metrics", "implementation details"]
            ),
            
            SourceType.REVIEW_PLATFORMS: SourceDefinition(
                source_type=SourceType.REVIEW_PLATFORMS,
                priority=SourcePriority.MEDIUM,
                description="Third-party review and comparison sites",
                url_patterns=[
                    URLPattern(
                        pattern=r"g2\.com/products/",
                        description="G2 product pages",
                        examples=["https://www.g2.com/products/benchling/reviews"]
                    ),
                    URLPattern(
                        pattern=r"capterra\.com/p/",
                        description="Capterra product pages",
                        examples=["https://www.capterra.com/p/123456/product-name/"]
                    ),
                    URLPattern(
                        pattern=r"trustradius\.com/products/",
                        description="TrustRadius product pages",
                        examples=["https://www.trustradius.com/products/product-name/reviews"]
                    )
                ],
                search_terms=["reviews", "ratings", "comparisons"],
                typical_content=["user reviews", "ratings", "feature comparisons"]
            ),
            
            SourceType.CHANGELOG: SourceDefinition(
                source_type=SourceType.CHANGELOG,
                priority=SourcePriority.MEDIUM,
                description="Product updates and release notes",
                url_patterns=[
                    URLPattern(
                        pattern=r"/(?:changelog|updates|releases|news)/?",
                        description="Changelog and updates",
                        examples=["https://benchling.com/changelog", "https://idbs.com/updates"]
                    ),
                    URLPattern(
                        pattern=r"/(?:blog|news)/?.*(?:release|update|feature)",
                        description="Release announcements",
                        examples=["https://company.com/blog/new-features"]
                    )
                ],
                search_terms=["changelog", "updates", "new features"],
                typical_content=["feature releases", "product updates", "roadmap hints"]
            ),
            
            SourceType.BLOG_POSTS: SourceDefinition(
                source_type=SourceType.BLOG_POSTS,
                priority=SourcePriority.LOW,
                description="Company blog posts and thought leadership",
                url_patterns=[
                    URLPattern(
                        pattern=r"/(?:blog|insights|resources)/?",
                        description="Blog and content pages",
                        examples=["https://benchling.com/blog", "https://idbs.com/insights"]
                    )
                ],
                search_terms=["blog", "insights", "thought leadership"],
                typical_content=["industry insights", "product announcements", "use cases"]
            ),
            
            SourceType.TECHNICAL_SPECS: SourceDefinition(
                source_type=SourceType.TECHNICAL_SPECS,
                priority=SourcePriority.MEDIUM,
                description="Technical specifications and API documentation",
                url_patterns=[
                    URLPattern(
                        pattern=r"/(?:api|developers|technical|specs)/?",
                        description="Technical documentation",
                        examples=["https://api.benchling.com", "https://developers.idbs.com"]
                    ),
                    URLPattern(
                        pattern=r"api\..*\.com",
                        description="API documentation subdomains",
                        examples=["https://api.company.com"]
                    )
                ],
                search_terms=["API", "technical specs", "developer docs"],
                typical_content=["API endpoints", "technical requirements", "integration guides"]
            )
        }
        
        return definitions
    
    def get_source_priorities(self, analysis_goal: str) -> Dict[SourceType, SourcePriority]:
        """
        Get source priorities based on analysis goal.
        
        Args:
            analysis_goal: Primary analysis goal
            
        Returns:
            Dictionary mapping source types to their priorities for this analysis
        """
        # Base priorities from definitions
        priorities = {
            source_type: definition.priority 
            for source_type, definition in self.source_definitions.items()
        }
        
        # Adjust priorities based on analysis goal
        if "pricing" in analysis_goal.lower():
            priorities[SourceType.PRICING_PAGE] = SourcePriority.CRITICAL
            priorities[SourceType.REVIEW_PLATFORMS] = SourcePriority.HIGH
            
        elif "feature" in analysis_goal.lower() or "competitive_benchmark" in analysis_goal.lower():
            priorities[SourceType.PRODUCT_PAGES] = SourcePriority.CRITICAL
            priorities[SourceType.DOCUMENTATION] = SourcePriority.CRITICAL
            priorities[SourceType.TECHNICAL_SPECS] = SourcePriority.HIGH
            
        elif "security" in analysis_goal.lower():
            priorities[SourceType.SECURITY_COMPLIANCE] = SourcePriority.CRITICAL
            priorities[SourceType.DOCUMENTATION] = SourcePriority.HIGH
            
        elif "market" in analysis_goal.lower():
            priorities[SourceType.CASE_STUDIES] = SourcePriority.HIGH
            priorities[SourceType.REVIEW_PLATFORMS] = SourcePriority.HIGH
            priorities[SourceType.BLOG_POSTS] = SourcePriority.MEDIUM
        
        return priorities
    
    def classify_url(self, url: str) -> Optional[SourceType]:
        """
        Classify a URL into a source type based on patterns.
        
        Args:
            url: URL to classify
            
        Returns:
            SourceType if pattern matches, None otherwise
        """
        for source_type, definition in self.source_definitions.items():
            for pattern in definition.url_patterns:
                if pattern.matches(url):
                    return source_type
        
        return None
    
    def get_search_terms_for_source(self, source_type: SourceType, competitor: str) -> List[str]:
        """
        Get search terms for finding a specific source type for a competitor.
        
        Args:
            source_type: Type of source to find
            competitor: Competitor name
            
        Returns:
            List of search terms
        """
        if source_type not in self.source_definitions:
            return []
        
        definition = self.source_definitions[source_type]
        base_terms = definition.search_terms
        
        # Combine competitor name with source-specific terms
        search_terms = []
        for term in base_terms:
            search_terms.extend([
                f"{competitor} {term}",
                f'"{competitor}" {term}',
                f"site:{competitor.lower().replace(' ', '')}.com {term}"
            ])
        
        return search_terms
    
    def get_expected_url_patterns(self, source_type: SourceType, competitor: str) -> List[str]:
        """
        Get expected URL patterns for a source type and competitor.
        
        Args:
            source_type: Type of source
            competitor: Competitor name
            
        Returns:
            List of expected URL patterns
        """
        if source_type not in self.source_definitions:
            return []
        
        definition = self.source_definitions[source_type]
        
        # Generate expected URLs based on patterns
        competitor_domain = competitor.lower().replace(' ', '').replace('.', '')
        expected_urls = []
        
        for pattern in definition.url_patterns:
            # Convert regex pattern to expected URL format
            if "/pricing" in pattern.pattern:
                expected_urls.extend([
                    f"https://{competitor_domain}.com/pricing",
                    f"https://www.{competitor_domain}.com/pricing",
                    f"https://{competitor_domain}.com/plans"
                ])
            elif "/docs" in pattern.pattern:
                expected_urls.extend([
                    f"https://docs.{competitor_domain}.com",
                    f"https://{competitor_domain}.com/docs",
                    f"https://help.{competitor_domain}.com"
                ])
            elif "/security" in pattern.pattern:
                expected_urls.extend([
                    f"https://{competitor_domain}.com/security",
                    f"https://{competitor_domain}.com/compliance"
                ])
        
        return expected_urls