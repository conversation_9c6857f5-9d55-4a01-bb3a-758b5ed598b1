"""
Research plan generation - the core orchestrator for competitive research strategy.

Transforms AnalysisIntent objects into comprehensive, actionable research plans
that guide evidence collection and competitor analysis.
"""

from typing import List, Dict, Optional, Set
from pydantic import BaseModel, Field
from datetime import datetime, timedelta
import structlog

from src.models import AnalysisIntent, PrimaryGoal
from .sources import SourceType, SourcePriority, SourceDiscovery
from .queries import QueryGenerator, SearchQuery, QueryType

logger = structlog.get_logger()

class CompetitorProfile(BaseModel):
    """Profile of a competitor for research planning."""
    name: str
    priority: int = Field(description="Research priority (1=highest)")
    known_urls: List[str] = Field(default_factory=list)
    expected_sources: Dict[SourceType, List[str]] = Field(default_factory=dict)
    discovery_confidence: float = Field(default=1.0, description="Confidence in competitor identification")

class SourceTarget(BaseModel):
    """Target source for research."""
    source_type: SourceType
    priority: SourcePriority
    competitor: Optional[str] = None
    expected_urls: List[str] = Field(default_factory=list)
    search_queries: List[SearchQuery] = Field(default_factory=list)
    estimated_effort: int = Field(default=1, description="Estimated effort (1-5 scale)")

class ResearchPhase(BaseModel):
    """A phase of the research plan."""
    name: str
    description: str
    targets: List[SourceTarget]
    estimated_duration_minutes: int
    dependencies: List[str] = Field(default_factory=list)
    success_criteria: List[str] = Field(default_factory=list)

class ResearchPlan(BaseModel):
    """Comprehensive research plan for competitive analysis."""
    analysis_id: str
    created_at: datetime = Field(default_factory=datetime.now)
    intent_summary: str
    
    # Competitor information
    competitors: List[CompetitorProfile]
    competitor_discovery_needed: bool = Field(default=False)
    
    # Research phases
    phases: List[ResearchPhase]
    
    # Query strategy
    total_queries: int
    priority_queries: List[SearchQuery]
    
    # Effort estimation
    estimated_total_duration_minutes: int
    estimated_sources_to_collect: int
    estimated_api_calls: int
    
    # Success metrics
    success_criteria: List[str]
    quality_thresholds: Dict[str, float] = Field(default_factory=dict)

class ResearchPlanner:
    """Generate comprehensive research plans from analysis intent."""
    
    def __init__(self):
        """Initialize research planner."""
        self.source_discovery = SourceDiscovery()
        self.query_generator = QueryGenerator()
        
        logger.info("Research planner initialized")
    
    def create_research_plan(self, intent: AnalysisIntent, analysis_id: str = None) -> ResearchPlan:
        """
        Create a comprehensive research plan from analysis intent.
        
        Args:
            intent: Structured analysis intent
            analysis_id: Optional analysis identifier
            
        Returns:
            Complete research plan
        """
        analysis_id = analysis_id or f"analysis_{int(datetime.now().timestamp())}"
        
        logger.info("Creating research plan", 
                   analysis_id=analysis_id,
                   industry=intent.market.industry_name,
                   primary_goal=intent.scope.primary_goal)
        
        # Generate competitor profiles
        competitors = self._generate_competitor_profiles(intent)
        
        # Determine if competitor discovery is needed
        competitor_discovery_needed = self._needs_competitor_discovery(intent, competitors)
        
        # Generate research phases
        phases = self._generate_research_phases(intent, competitors)
        
        # Generate search queries
        all_queries = self.query_generator.generate_research_queries(intent)
        priority_queries = [q for q in all_queries if q.priority <= 2]
        
        # Calculate effort estimates
        effort_estimates = self._calculate_effort_estimates(phases, all_queries)
        
        # Define success criteria
        success_criteria = self._define_success_criteria(intent)
        quality_thresholds = self._define_quality_thresholds(intent)
        
        plan = ResearchPlan(
            analysis_id=analysis_id,
            intent_summary=self._create_intent_summary(intent),
            competitors=competitors,
            competitor_discovery_needed=competitor_discovery_needed,
            phases=phases,
            total_queries=len(all_queries),
            priority_queries=priority_queries,
            estimated_total_duration_minutes=effort_estimates["total_duration"],
            estimated_sources_to_collect=effort_estimates["total_sources"],
            estimated_api_calls=effort_estimates["total_api_calls"],
            success_criteria=success_criteria,
            quality_thresholds=quality_thresholds
        )
        
        logger.info("Research plan created successfully",
                   analysis_id=analysis_id,
                   competitors_count=len(competitors),
                   phases_count=len(phases),
                   total_queries=len(all_queries),
                   estimated_duration=effort_estimates["total_duration"])
        
        return plan
    
    def _generate_competitor_profiles(self, intent: AnalysisIntent) -> List[CompetitorProfile]:
        """Generate competitor profiles for research planning."""
        profiles = []
        
        # Process explicitly mentioned competitors
        if intent.competition and intent.competition.competitor_list_explicit:
            for i, competitor in enumerate(intent.competition.competitor_list_explicit):
                profile = CompetitorProfile(
                    name=competitor,
                    priority=i + 1,  # First mentioned = highest priority
                    discovery_confidence=1.0
                )
                
                # Generate expected sources for this competitor
                profile.expected_sources = self._generate_expected_sources(competitor, intent)
                
                profiles.append(profile)
        
        # If no competitors specified, we'll need discovery
        if not profiles:
            logger.info("No explicit competitors found, competitor discovery will be needed")
        
        return profiles
    
    def _generate_expected_sources(self, competitor: str, intent: AnalysisIntent) -> Dict[SourceType, List[str]]:
        """Generate expected sources for a competitor."""
        expected_sources = {}
        
        # Get source priorities for this analysis
        source_priorities = self.source_discovery.get_source_priorities(intent.scope.primary_goal.value)
        
        # Generate expected URLs for high-priority sources
        for source_type, priority in source_priorities.items():
            if priority.value in ["critical", "high"]:
                expected_urls = self.source_discovery.get_expected_url_patterns(source_type, competitor)
                if expected_urls:
                    expected_sources[source_type] = expected_urls
        
        return expected_sources
    
    def _needs_competitor_discovery(self, intent: AnalysisIntent, competitors: List[CompetitorProfile]) -> bool:
        """Determine if competitor discovery is needed."""
        # Need discovery if no competitors specified
        if not competitors:
            return True
        
        # Need discovery if analysis goal suggests broader market view
        discovery_goals = [
            PrimaryGoal.market_entry_assessment,
            PrimaryGoal.market_size_forecast,
            PrimaryGoal.competitive_benchmark_product
        ]
        
        if intent.scope.primary_goal in discovery_goals:
            return True
        
        # Need discovery if fewer than 3 competitors for comprehensive analysis
        if len(competitors) < 3 and intent.outputs.depth.value == "deep_dive":
            return True
        
        return False
    
    def _generate_research_phases(self, intent: AnalysisIntent, competitors: List[CompetitorProfile]) -> List[ResearchPhase]:
        """Generate research phases based on analysis requirements."""
        phases = []
        
        # Phase 1: Competitor Discovery (if needed)
        if self._needs_competitor_discovery(intent, competitors):
            discovery_phase = self._create_competitor_discovery_phase(intent)
            phases.append(discovery_phase)
        
        # Phase 2: Source Discovery
        source_discovery_phase = self._create_source_discovery_phase(intent, competitors)
        phases.append(source_discovery_phase)
        
        # Phase 3: Content Collection
        content_collection_phase = self._create_content_collection_phase(intent, competitors)
        phases.append(content_collection_phase)
        
        # Phase 4: Specialized Research (based on analysis goal)
        specialized_phase = self._create_specialized_research_phase(intent, competitors)
        if specialized_phase:
            phases.append(specialized_phase)
        
        return phases
    
    def _create_competitor_discovery_phase(self, intent: AnalysisIntent) -> ResearchPhase:
        """Create competitor discovery phase."""
        discovery_queries = self.query_generator._generate_competitor_discovery_queries(intent)
        
        targets = [
            SourceTarget(
                source_type=SourceType.INDUSTRY_REPORTS,
                priority=SourcePriority.HIGH,
                search_queries=discovery_queries[:5],  # Top 5 discovery queries
                estimated_effort=3
            ),
            SourceTarget(
                source_type=SourceType.REVIEW_PLATFORMS,
                priority=SourcePriority.HIGH,
                search_queries=discovery_queries[5:10],  # Next 5 queries
                estimated_effort=2
            )
        ]
        
        return ResearchPhase(
            name="Competitor Discovery",
            description="Identify additional competitors and market players",
            targets=targets,
            estimated_duration_minutes=20,
            success_criteria=[
                "Identify at least 5 relevant competitors",
                "Validate competitor relevance to analysis scope",
                "Prioritize competitors by market position"
            ]
        )
    
    def _create_source_discovery_phase(self, intent: AnalysisIntent, competitors: List[CompetitorProfile]) -> ResearchPhase:
        """Create source discovery phase."""
        targets = []
        
        # Get source priorities
        source_priorities = self.source_discovery.get_source_priorities(intent.scope.primary_goal.value)
        
        # Create targets for each high-priority source type
        for source_type, priority in source_priorities.items():
            if priority.value in ["critical", "high"]:
                # Generate queries for this source type across all competitors
                source_queries = []
                for competitor in competitors:
                    queries = self.query_generator._generate_source_discovery_queries(intent, competitor.name)
                    source_queries.extend([q for q in queries if q.source_type == source_type])
                
                if source_queries:
                    targets.append(SourceTarget(
                        source_type=source_type,
                        priority=priority,
                        search_queries=source_queries[:10],  # Limit queries per source type
                        estimated_effort=2 if priority == SourcePriority.CRITICAL else 1
                    ))
        
        return ResearchPhase(
            name="Source Discovery",
            description="Discover and validate key sources for each competitor",
            targets=targets,
            estimated_duration_minutes=30,
            dependencies=["Competitor Discovery"] if self._needs_competitor_discovery(intent, competitors) else [],
            success_criteria=[
                "Find official websites for all competitors",
                "Locate pricing pages for 80% of competitors",
                "Identify documentation sources for key competitors"
            ]
        )
    
    def _create_content_collection_phase(self, intent: AnalysisIntent, competitors: List[CompetitorProfile]) -> ResearchPhase:
        """Create content collection phase."""
        targets = []
        
        # Prioritize content collection based on analysis goal
        if intent.scope.primary_goal == PrimaryGoal.competitive_benchmark_feature:
            priority_sources = [SourceType.PRODUCT_PAGES, SourceType.DOCUMENTATION, SourceType.TECHNICAL_SPECS]
        elif intent.scope.primary_goal == PrimaryGoal.pricing_analysis:
            priority_sources = [SourceType.PRICING_PAGE, SourceType.PRODUCT_PAGES]
        elif "security" in intent.scope.primary_goal.value:
            priority_sources = [SourceType.SECURITY_COMPLIANCE, SourceType.DOCUMENTATION]
        else:
            priority_sources = [SourceType.VENDOR_SITE, SourceType.PRODUCT_PAGES, SourceType.PRICING_PAGE]
        
        for source_type in priority_sources:
            targets.append(SourceTarget(
                source_type=source_type,
                priority=SourcePriority.CRITICAL,
                estimated_effort=3
            ))
        
        return ResearchPhase(
            name="Content Collection",
            description="Collect and extract content from identified sources",
            targets=targets,
            estimated_duration_minutes=45,
            dependencies=["Source Discovery"],
            success_criteria=[
                "Successfully extract content from 90% of identified sources",
                "Maintain content quality above confidence threshold",
                "Capture screenshots for visual evidence"
            ]
        )
    
    def _create_specialized_research_phase(self, intent: AnalysisIntent, competitors: List[CompetitorProfile]) -> Optional[ResearchPhase]:
        """Create specialized research phase based on analysis goal."""
        
        if intent.scope.primary_goal == PrimaryGoal.competitive_benchmark_feature:
            return ResearchPhase(
                name="Feature Analysis",
                description="Deep dive into feature comparison and analysis",
                targets=[
                    SourceTarget(
                        source_type=SourceType.DOCUMENTATION,
                        priority=SourcePriority.CRITICAL,
                        estimated_effort=4
                    ),
                    SourceTarget(
                        source_type=SourceType.TECHNICAL_SPECS,
                        priority=SourcePriority.HIGH,
                        estimated_effort=3
                    )
                ],
                estimated_duration_minutes=35,
                dependencies=["Content Collection"],
                success_criteria=[
                    "Map all requested features across competitors",
                    "Achieve 80% feature coverage confidence",
                    "Identify feature gaps and opportunities"
                ]
            )
        
        elif intent.scope.primary_goal == PrimaryGoal.pricing_analysis:
            return ResearchPhase(
                name="Pricing Intelligence",
                description="Comprehensive pricing analysis and normalization",
                targets=[
                    SourceTarget(
                        source_type=SourceType.PRICING_PAGE,
                        priority=SourcePriority.CRITICAL,
                        estimated_effort=4
                    ),
                    SourceTarget(
                        source_type=SourceType.REVIEW_PLATFORMS,
                        priority=SourcePriority.HIGH,
                        estimated_effort=2
                    )
                ],
                estimated_duration_minutes=30,
                dependencies=["Content Collection"],
                success_criteria=[
                    "Extract pricing for 90% of competitors",
                    "Normalize pricing models and tiers",
                    "Identify pricing trends and patterns"
                ]
            )
        
        return None
    
    def _calculate_effort_estimates(self, phases: List[ResearchPhase], queries: List[SearchQuery]) -> Dict[str, int]:
        """Calculate effort estimates for the research plan."""
        total_duration = sum(phase.estimated_duration_minutes for phase in phases)
        
        # Estimate sources based on competitors and source types
        total_sources = 0
        for phase in phases:
            for target in phase.targets:
                total_sources += len(target.search_queries) * 2  # Rough estimate
        
        # API calls include search queries plus content fetching
        total_api_calls = len(queries) + total_sources
        
        return {
            "total_duration": total_duration,
            "total_sources": total_sources,
            "total_api_calls": total_api_calls
        }
    
    def _define_success_criteria(self, intent: AnalysisIntent) -> List[str]:
        """Define success criteria for the research plan."""
        criteria = [
            "Complete research within estimated timeframe",
            "Achieve minimum confidence thresholds for all insights",
            "Collect evidence from diverse source types",
            "Maintain compliance with robots.txt and rate limits"
        ]
        
        # Add goal-specific criteria
        if intent.scope.primary_goal == PrimaryGoal.competitive_benchmark_feature:
            criteria.extend([
                "Map 90% of requested features across competitors",
                "Provide evidence citations for all feature claims",
                "Identify at least 3 competitive gaps or opportunities"
            ])
        
        elif intent.scope.primary_goal == PrimaryGoal.pricing_analysis:
            criteria.extend([
                "Extract pricing information for 80% of competitors",
                "Normalize pricing models for comparison",
                "Identify pricing positioning and trends"
            ])
        
        return criteria
    
    def _define_quality_thresholds(self, intent: AnalysisIntent) -> Dict[str, float]:
        """Define quality thresholds for the research plan."""
        base_threshold = intent.outputs.confidence_threshold or 0.8
        
        return {
            "minimum_confidence": base_threshold,
            "source_diversity": 0.7,  # At least 70% of source types should be covered
            "competitor_coverage": 0.8,  # At least 80% of competitors should have data
            "evidence_quality": base_threshold,
            "citation_coverage": 1.0  # 100% of insights must have citations
        }
    
    def _create_intent_summary(self, intent: AnalysisIntent) -> str:
        """Create a human-readable summary of the analysis intent."""
        summary_parts = [
            f"Primary Goal: {intent.scope.primary_goal.value.replace('_', ' ').title()}",
            f"Industry: {intent.market.industry_name}"
        ]
        
        if intent.market.geographies:
            summary_parts.append(f"Geography: {', '.join(intent.market.geographies)}")
        
        if intent.competition and intent.competition.competitor_list_explicit:
            competitors = ', '.join(intent.competition.competitor_list_explicit[:3])
            if len(intent.competition.competitor_list_explicit) > 3:
                competitors += f" and {len(intent.competition.competitor_list_explicit) - 3} others"
            summary_parts.append(f"Competitors: {competitors}")
        
        if intent.capabilities and intent.capabilities.feature_taxonomy:
            features = ', '.join(intent.capabilities.feature_taxonomy[:3])
            if len(intent.capabilities.feature_taxonomy) > 3:
                features += f" and {len(intent.capabilities.feature_taxonomy) - 3} others"
            summary_parts.append(f"Key Features: {features}")
        
        return " | ".join(summary_parts)