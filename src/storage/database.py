"""
Database configuration and initialization.
"""

import os
from contextlib import asynccontextmanager
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import structlog

logger = structlog.get_logger()

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./ica_app.db")

# Create engine
engine = create_engine(
    DATABASE_URL,
    echo=os.getenv("SQL_ECHO", "false").lower() == "true"
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create base class for models
Base = declarative_base()

async def init_database():
    """Initialize database tables."""
    try:
        logger.info("Initializing database", database_url=DATABASE_URL)
        
        # Import models to ensure they're registered
        from src.storage.models import (
            AnalysisRecord, DocumentRecord, FindingR<PERSON>ord, 
            FeatureMatrixRecord, PricingRecord, ScreenshotRecord
        )
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        
        logger.info("Database initialization completed")
        
    except Exception as e:
        logger.error("Database initialization failed", error=str(e))
        raise

async def run_migrations():
    """Run database migrations using Alembic."""
    try:
        from alembic.config import Config
        from alembic import command
        
        logger.info("Running database migrations")
        
        # Create Alembic config
        alembic_cfg = Config("alembic.ini")
        alembic_cfg.set_main_option("sqlalchemy.url", DATABASE_URL)
        
        # Run migrations
        command.upgrade(alembic_cfg, "head")
        
        logger.info("Database migrations completed")
        
    except Exception as e:
        logger.error("Database migration failed", error=str(e))
        # Fall back to creating tables directly
        await init_database()

@asynccontextmanager
async def get_db_session():
    """Get database session with automatic cleanup."""
    session = SessionLocal()
    try:
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        logger.error("Database session error", error=str(e))
        raise
    finally:
        session.close()

def get_db():
    """Dependency for FastAPI to get database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()