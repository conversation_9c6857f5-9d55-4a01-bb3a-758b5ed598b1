"""
SQLAlchemy database models for the Industry & Competitor Analysis App.
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import Column, Integer, String, Text, DateTime, Float, Boolean, JSON, ForeignKey, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid

from src.storage.database import Base
from src.models.enums import AnalysisStatus, SourceType, ClaimTopic, SupportLevel

class AnalysisRecord(Base):
    """Main analysis record storing the complete analysis workflow."""
    
    __tablename__ = "analyses"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    status = Column(Enum(AnalysisStatus), nullable=False, default=AnalysisStatus.pending)
    
    # Store the original intent and final results as JSON
    intent_json = Column(JSON, nullable=False)
    results_json = Column(JSON, nullable=True)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    completed_at = Column(DateTime, nullable=True)
    processing_time_seconds = Column(Float, nullable=True)
    
    # User/session info (for future multi-tenancy)
    user_id = Column(String, nullable=True)
    session_id = Column(String, nullable=True)
    
    # Relationships
    documents = relationship("DocumentRecord", back_populates="analysis", cascade="all, delete-orphan")
    findings = relationship("FindingRecord", back_populates="analysis", cascade="all, delete-orphan")
    screenshots = relationship("ScreenshotRecord", back_populates="analysis", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<AnalysisRecord(id={self.id}, status={self.status}, created_at={self.created_at})>"

class DocumentRecord(Base):
    """Evidence documents collected during research."""
    
    __tablename__ = "documents"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    analysis_id = Column(String, ForeignKey("analyses.id"), nullable=False)
    
    # Document metadata
    url = Column(String, nullable=False)
    source_type = Column(Enum(SourceType), nullable=False)
    title = Column(String, nullable=True)
    
    # Content
    text_content = Column(Text, nullable=True)
    content_hash = Column(String, nullable=False, unique=True)  # For deduplication
    
    # Timestamps
    published_at = Column(DateTime, nullable=True)
    scraped_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Metadata
    language = Column(String, default="en", nullable=False)
    word_count = Column(Integer, nullable=True)
    
    # Relationships
    analysis = relationship("AnalysisRecord", back_populates="documents")
    findings = relationship("FindingRecord", back_populates="document")
    
    def __repr__(self):
        return f"<DocumentRecord(id={self.id}, url={self.url}, source_type={self.source_type})>"

class FindingRecord(Base):
    """Individual findings/claims extracted from documents."""
    
    __tablename__ = "findings"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    analysis_id = Column(String, ForeignKey("analyses.id"), nullable=False)
    document_id = Column(String, ForeignKey("documents.id"), nullable=False)
    
    # Claim details
    topic = Column(Enum(ClaimTopic), nullable=False)
    target = Column(String, nullable=False)  # Company, product, or feature
    claim_text = Column(Text, nullable=False)
    
    # Evidence
    evidence_quote = Column(Text, nullable=False)
    start_offset = Column(Integer, nullable=True)
    end_offset = Column(Integer, nullable=True)
    
    # Quality metrics
    confidence = Column(Float, nullable=False)
    verified = Column(Boolean, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relationships
    analysis = relationship("AnalysisRecord", back_populates="findings")
    document = relationship("DocumentRecord", back_populates="findings")
    
    def __repr__(self):
        return f"<FindingRecord(id={self.id}, topic={self.topic}, target={self.target})>"

class FeatureMatrixRecord(Base):
    """Feature comparison matrix data."""
    
    __tablename__ = "feature_matrix"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    analysis_id = Column(String, ForeignKey("analyses.id"), nullable=False)
    
    # Feature comparison data
    competitor = Column(String, nullable=False)
    feature = Column(String, nullable=False)
    support_level = Column(Enum(SupportLevel), nullable=False)
    confidence = Column(Float, nullable=False)
    
    # Evidence
    evidence_url = Column(String, nullable=True)
    evidence_quote = Column(Text, nullable=True)
    screenshot_path = Column(String, nullable=True)
    
    # Metadata
    notes = Column(Text, nullable=True)
    last_verified = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relationships
    analysis = relationship("AnalysisRecord")
    
    def __repr__(self):
        return f"<FeatureMatrixRecord(competitor={self.competitor}, feature={self.feature}, support={self.support_level})>"

class PricingRecord(Base):
    """Normalized pricing information."""
    
    __tablename__ = "pricing"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    analysis_id = Column(String, ForeignKey("analyses.id"), nullable=False)
    
    # Pricing details
    competitor = Column(String, nullable=False)
    tier_name = Column(String, nullable=False)
    
    # Price information
    price_amount = Column(Float, nullable=True)
    price_currency = Column(String, nullable=True)
    billing_cycle = Column(String, nullable=True)  # monthly, annual, one_time
    unit = Column(String, nullable=True)  # user, seat, workspace, etc.
    
    # Features and limitations
    features_included = Column(JSON, nullable=True)  # List of features
    limitations = Column(JSON, nullable=True)  # List of limitations
    
    # Additional pricing info
    overages_amount = Column(Float, nullable=True)
    overages_currency = Column(String, nullable=True)
    discounts = Column(String, nullable=True)
    free_trial = Column(String, nullable=True)
    
    # Evidence
    evidence_url = Column(String, nullable=False)
    last_updated = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relationships
    analysis = relationship("AnalysisRecord")
    
    def __repr__(self):
        return f"<PricingRecord(competitor={self.competitor}, tier={self.tier_name}, price={self.price_amount})>"

class ScreenshotRecord(Base):
    """Screenshot evidence captured during analysis."""
    
    __tablename__ = "screenshots"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    analysis_id = Column(String, ForeignKey("analyses.id"), nullable=False)
    
    # Screenshot details
    url = Column(String, nullable=False)
    label = Column(String, nullable=True)
    file_path = Column(String, nullable=False)
    
    # Capture metadata
    viewport_width = Column(Integer, nullable=True)
    viewport_height = Column(Integer, nullable=True)
    selectors = Column(JSON, nullable=True)  # List of CSS selectors captured
    
    # Timestamps
    captured_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # File metadata
    file_size_bytes = Column(Integer, nullable=True)
    image_format = Column(String, default="png", nullable=False)
    
    # Relationships
    analysis = relationship("AnalysisRecord", back_populates="screenshots")
    
    def __repr__(self):
        return f"<ScreenshotRecord(id={self.id}, url={self.url}, label={self.label})>"