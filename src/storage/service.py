"""
Database service layer for analysis operations.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_

from src.storage.models import (
    AnalysisRecord, 
    DocumentRecord, 
    FindingRecord, 
    FeatureMatrixRecord, 
    PricingRecord, 
    ScreenshotRecord
)
from src.models import AnalysisIntent
import structlog

logger = structlog.get_logger()

class AnalysisService:
    """Service for managing analysis records."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_analysis(self, intent: AnalysisIntent, user_id: Optional[str] = None) -> AnalysisRecord:
        """Create a new analysis record."""
        analysis = AnalysisRecord(
            status=AnalysisStatus.pending,
            intent_json=intent.model_dump(),
            user_id=user_id,
            created_at=datetime.utcnow()
        )
        
        self.db.add(analysis)
        self.db.commit()
        self.db.refresh(analysis)
        
        logger.info("Analysis created", analysis_id=analysis.id, industry=intent.market.industry_name)
        return analysis
    
    def get_analysis(self, analysis_id: str) -> Optional[AnalysisRecord]:
        """Get analysis by ID."""
        return self.db.query(AnalysisRecord).filter(AnalysisRecord.id == analysis_id).first()
    
    def update_analysis_status(self, analysis_id: str, status: AnalysisStatus) -> bool:
        """Update analysis status."""
        analysis = self.get_analysis(analysis_id)
        if not analysis:
            return False
        
        analysis.status = status
        if status == AnalysisStatus.completed:
            analysis.completed_at = datetime.utcnow()
            if analysis.created_at:
                analysis.processing_time_seconds = (
                    analysis.completed_at - analysis.created_at
                ).total_seconds()
        
        self.db.commit()
        logger.info("Analysis status updated", analysis_id=analysis_id, status=status)
        return True
    
    def update_analysis_results(self, analysis_id: str, results: Dict[str, Any]) -> bool:
        """Update analysis results."""
        analysis = self.get_analysis(analysis_id)
        if not analysis:
            return False
        
        analysis.results_json = results
        self.db.commit()
        logger.info("Analysis results updated", analysis_id=analysis_id)
        return True
    
    def list_analyses(self, limit: int = 50, offset: int = 0, user_id: Optional[str] = None) -> List[AnalysisRecord]:
        """List analyses with pagination."""
        query = self.db.query(AnalysisRecord)
        
        if user_id:
            query = query.filter(AnalysisRecord.user_id == user_id)
        
        return query.order_by(desc(AnalysisRecord.created_at)).offset(offset).limit(limit).all()

class DocumentService:
    """Service for managing document records."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_document(self, analysis_id: str, url: str, source_type: str, 
                       title: Optional[str] = None, text_content: Optional[str] = None,
                       content_hash: Optional[str] = None, **kwargs) -> DocumentRecord:
        """Create a new document record."""
        document = DocumentRecord(
            analysis_id=analysis_id,
            url=url,
            source_type=source_type,
            title=title,
            text_content=text_content,
            content_hash=content_hash or self._generate_content_hash(text_content or ""),
            scraped_at=datetime.utcnow(),
            **kwargs
        )
        
        self.db.add(document)
        self.db.commit()
        self.db.refresh(document)
        
        logger.info("Document created", document_id=document.id, url=url, source_type=source_type)
        return document
    
    def get_documents_by_analysis(self, analysis_id: str) -> List[DocumentRecord]:
        """Get all documents for an analysis."""
        return self.db.query(DocumentRecord).filter(
            DocumentRecord.analysis_id == analysis_id
        ).all()
    
    def find_duplicate_document(self, content_hash: str) -> Optional[DocumentRecord]:
        """Find document with same content hash."""
        return self.db.query(DocumentRecord).filter(
            DocumentRecord.content_hash == content_hash
        ).first()
    
    def _generate_content_hash(self, content: str) -> str:
        """Generate hash for content deduplication."""
        import hashlib
        return hashlib.sha256(content.encode()).hexdigest()

class FindingService:
    """Service for managing finding records."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_finding(self, analysis_id: str, document_id: str, topic: str,
                      target: str, claim_text: str, evidence_quote: str,
                      confidence: float, **kwargs) -> FindingRecord:
        """Create a new finding record."""
        finding = FindingRecord(
            analysis_id=analysis_id,
            document_id=document_id,
            topic=topic,
            target=target,
            claim_text=claim_text,
            evidence_quote=evidence_quote,
            confidence=confidence,
            created_at=datetime.utcnow(),
            **kwargs
        )
        
        self.db.add(finding)
        self.db.commit()
        self.db.refresh(finding)
        
        logger.info("Finding created", finding_id=finding.id, topic=topic, target=target)
        return finding
    
    def get_findings_by_analysis(self, analysis_id: str, topic: Optional[str] = None) -> List[FindingRecord]:
        """Get findings for an analysis, optionally filtered by topic."""
        query = self.db.query(FindingRecord).filter(FindingRecord.analysis_id == analysis_id)
        
        if topic:
            query = query.filter(FindingRecord.topic == topic)
        
        return query.all()

class FeatureMatrixService:
    """Service for managing feature matrix records."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_feature_entry(self, analysis_id: str, competitor: str, feature: str,
                           support_level: str, confidence: float, **kwargs) -> FeatureMatrixRecord:
        """Create a feature matrix entry."""
        entry = FeatureMatrixRecord(
            analysis_id=analysis_id,
            competitor=competitor,
            feature=feature,
            support_level=support_level,
            confidence=confidence,
            last_verified=datetime.utcnow(),
            **kwargs
        )
        
        self.db.add(entry)
        self.db.commit()
        self.db.refresh(entry)
        
        logger.info("Feature matrix entry created", 
                   competitor=competitor, feature=feature, support=support_level)
        return entry
    
    def get_feature_matrix(self, analysis_id: str) -> List[FeatureMatrixRecord]:
        """Get complete feature matrix for an analysis."""
        return self.db.query(FeatureMatrixRecord).filter(
            FeatureMatrixRecord.analysis_id == analysis_id
        ).all()
    
    def get_competitor_features(self, analysis_id: str, competitor: str) -> List[FeatureMatrixRecord]:
        """Get all features for a specific competitor."""
        return self.db.query(FeatureMatrixRecord).filter(
            and_(
                FeatureMatrixRecord.analysis_id == analysis_id,
                FeatureMatrixRecord.competitor == competitor
            )
        ).all()

class PricingService:
    """Service for managing pricing records."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_pricing_entry(self, analysis_id: str, competitor: str, tier_name: str,
                           evidence_url: str, **kwargs) -> PricingRecord:
        """Create a pricing entry."""
        entry = PricingRecord(
            analysis_id=analysis_id,
            competitor=competitor,
            tier_name=tier_name,
            evidence_url=evidence_url,
            last_updated=datetime.utcnow(),
            **kwargs
        )
        
        self.db.add(entry)
        self.db.commit()
        self.db.refresh(entry)
        
        logger.info("Pricing entry created", competitor=competitor, tier=tier_name)
        return entry
    
    def get_pricing_data(self, analysis_id: str) -> List[PricingRecord]:
        """Get all pricing data for an analysis."""
        return self.db.query(PricingRecord).filter(
            PricingRecord.analysis_id == analysis_id
        ).all()

class ScreenshotService:
    """Service for managing screenshot records."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_screenshot(self, analysis_id: str, url: str, file_path: str,
                         **kwargs) -> ScreenshotRecord:
        """Create a screenshot record."""
        screenshot = ScreenshotRecord(
            analysis_id=analysis_id,
            url=url,
            file_path=file_path,
            captured_at=datetime.utcnow(),
            **kwargs
        )
        
        self.db.add(screenshot)
        self.db.commit()
        self.db.refresh(screenshot)
        
        logger.info("Screenshot record created", url=url, file_path=file_path)
        return screenshot
    
    def get_screenshots(self, analysis_id: str) -> List[ScreenshotRecord]:
        """Get all screenshots for an analysis."""
        return self.db.query(ScreenshotRecord).filter(
            ScreenshotRecord.analysis_id == analysis_id
        ).all()