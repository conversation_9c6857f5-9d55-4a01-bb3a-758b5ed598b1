"""
Prompt injection detection and prevention.

Implements OWASP LLM Top-10 security measures for prompt injection attacks.
"""

import re
from typing import List, Dict, Any
from dataclasses import dataclass
import structlog

logger = structlog.get_logger()

@dataclass
class SecurityScanResult:
    """Result of security scan."""
    is_safe: bool
    threats_detected: List[str]
    risk_score: float  # 0.0 (safe) to 1.0 (high risk)
    recommended_action: str  # allow, flag, block

class PromptInjectionDetector:
    """Detect and prevent prompt injection attacks."""
    
    def __init__(self):
        """Initialize the detector with known injection patterns."""
        self.injection_patterns = [
            # Direct instruction overrides
            r"ignore\s+(?:previous|all|above)\s+instructions?",
            r"forget\s+(?:everything|all|previous|above)",
            r"disregard\s+(?:previous|all|above)\s+(?:instructions?|prompts?)",
            
            # System prompt manipulation
            r"system\s*[:.]?\s*(?:prompt|override|mode|instructions?)",
            r"act\s+as\s+(?:a\s+)?(?:different|new|admin|root|system)",
            r"you\s+are\s+now\s+(?:a\s+)?(?:different|admin|system)",
            
            # Role manipulation
            r"pretend\s+(?:to\s+be|you\s+are)\s+(?:a\s+)?(?:different|admin|system)",
            r"roleplay\s+as\s+(?:a\s+)?(?:different|admin|system)",
            r"simulate\s+(?:being\s+)?(?:a\s+)?(?:different|admin|system)",
            
            # Output manipulation
            r"output\s+(?:only|just)\s+(?:the\s+)?(?:following|this)",
            r"respond\s+(?:only|just)\s+with\s+(?:the\s+)?(?:following|this)",
            r"say\s+(?:only|just|exactly)\s+(?:the\s+)?(?:following|this)",
            
            # Instruction injection
            r"new\s+(?:instruction|task|prompt|system)",
            r"updated\s+(?:instruction|task|prompt|system)",
            r"override\s+(?:instruction|task|prompt|system)",
            
            # Delimiter attacks
            r"---+\s*(?:new|system|admin|override)",
            r"===+\s*(?:new|system|admin|override)",
            r"\*\*\*+\s*(?:new|system|admin|override)",
            
            # Encoding attempts
            r"base64\s*[:=]\s*[A-Za-z0-9+/=]+",
            r"hex\s*[:=]\s*[0-9a-fA-F]+",
            r"unicode\s*[:=]\s*\\u[0-9a-fA-F]{4}",
            
            # Jailbreak attempts
            r"jailbreak",
            r"break\s+(?:out|free)\s+(?:of|from)",
            r"escape\s+(?:the|your)\s+(?:system|constraints?|limitations?)",
            
            # Developer mode
            r"developer\s+mode",
            r"debug\s+mode",
            r"admin\s+mode",
            r"god\s+mode",
            
            # Hypothetical scenarios
            r"hypothetically\s*,?\s*if\s+you\s+(?:were|could|had)",
            r"imagine\s+(?:if\s+)?you\s+(?:were|could|had)",
            r"what\s+if\s+you\s+(?:were|could|had)",
        ]
        
        # Compile patterns for better performance
        self.compiled_patterns = [
            re.compile(pattern, re.IGNORECASE | re.MULTILINE)
            for pattern in self.injection_patterns
        ]
        
        logger.info("Prompt injection detector initialized", 
                   pattern_count=len(self.injection_patterns))
    
    def scan_input(self, user_input: str) -> SecurityScanResult:
        """
        Scan user input for potential injection attempts.
        
        Args:
            user_input: User-provided text to scan
            
        Returns:
            SecurityScanResult with threat assessment
        """
        threats_detected = []
        risk_score = 0.0
        
        # Check against known injection patterns
        for i, pattern in enumerate(self.compiled_patterns):
            matches = pattern.findall(user_input)
            if matches:
                pattern_text = self.injection_patterns[i]
                threats_detected.append(f"Injection pattern detected: {pattern_text}")
                
                # Increase risk score based on pattern severity
                if any(keyword in pattern_text.lower() for keyword in ["system", "admin", "override"]):
                    risk_score += 0.3  # High severity
                elif any(keyword in pattern_text.lower() for keyword in ["ignore", "forget", "disregard"]):
                    risk_score += 0.2  # Medium severity
                else:
                    risk_score += 0.1  # Low severity
        
        # Additional heuristics
        risk_score += self._check_suspicious_patterns(user_input)
        risk_score += self._check_length_anomalies(user_input)
        risk_score += self._check_encoding_attempts(user_input)
        
        # Cap risk score at 1.0
        risk_score = min(risk_score, 1.0)
        
        # Determine recommended action
        if risk_score >= 0.8:
            recommended_action = "block"
        elif risk_score >= 0.5:
            recommended_action = "flag"
        else:
            recommended_action = "allow"
        
        is_safe = risk_score < 0.3
        
        if threats_detected:
            logger.warning(
                "Potential prompt injection detected",
                threats_count=len(threats_detected),
                risk_score=risk_score,
                recommended_action=recommended_action,
                input_length=len(user_input)
            )
        
        return SecurityScanResult(
            is_safe=is_safe,
            threats_detected=threats_detected,
            risk_score=risk_score,
            recommended_action=recommended_action
        )
    
    def _check_suspicious_patterns(self, text: str) -> float:
        """Check for suspicious patterns that might indicate injection."""
        risk_increase = 0.0
        
        # Excessive special characters
        special_char_ratio = len(re.findall(r'[^\w\s]', text)) / len(text) if text else 0
        if special_char_ratio > 0.3:
            risk_increase += 0.1
        
        # Repeated delimiters
        if re.search(r'[-=*]{5,}', text):
            risk_increase += 0.1
        
        # Multiple newlines (potential delimiter injection)
        if text.count('\n') > 10:
            risk_increase += 0.05
        
        # Suspicious keywords clustering
        suspicious_keywords = [
            'system', 'admin', 'root', 'override', 'bypass', 'hack',
            'jailbreak', 'exploit', 'vulnerability', 'injection'
        ]
        
        keyword_count = sum(1 for keyword in suspicious_keywords if keyword.lower() in text.lower())
        if keyword_count >= 3:
            risk_increase += 0.2
        
        return risk_increase
    
    def _check_length_anomalies(self, text: str) -> float:
        """Check for unusual length patterns."""
        risk_increase = 0.0
        
        # Extremely long input (potential buffer overflow attempt)
        if len(text) > 10000:
            risk_increase += 0.1
        
        # Very short but suspicious input
        if len(text) < 50 and any(word in text.lower() for word in ['system', 'admin', 'override']):
            risk_increase += 0.15
        
        return risk_increase
    
    def _check_encoding_attempts(self, text: str) -> float:
        """Check for potential encoding-based injection attempts."""
        risk_increase = 0.0
        
        # Base64-like patterns
        if re.search(r'[A-Za-z0-9+/]{20,}={0,2}', text):
            risk_increase += 0.1
        
        # Hex encoding patterns
        if re.search(r'(?:0x|\\x)[0-9a-fA-F]{2,}', text):
            risk_increase += 0.1
        
        # Unicode escape sequences
        if re.search(r'\\u[0-9a-fA-F]{4}', text):
            risk_increase += 0.05
        
        # URL encoding
        if re.search(r'%[0-9a-fA-F]{2}', text):
            risk_increase += 0.05
        
        return risk_increase
    
    def sanitize_input(self, text: str, aggressive: bool = False) -> str:
        """
        Sanitize input by removing or neutralizing potential injection attempts.
        
        Args:
            text: Input text to sanitize
            aggressive: Whether to use aggressive sanitization
            
        Returns:
            Sanitized text
        """
        sanitized = text
        
        # Remove or replace known injection patterns
        for pattern in self.compiled_patterns:
            if aggressive:
                sanitized = pattern.sub('[REDACTED]', sanitized)
            else:
                sanitized = pattern.sub(lambda m: m.group(0).replace('ignore', 'consider'), sanitized)
        
        # Remove excessive special characters
        if aggressive:
            sanitized = re.sub(r'[-=*]{3,}', '---', sanitized)
            sanitized = re.sub(r'\n{3,}', '\n\n', sanitized)
        
        # Limit length
        if len(sanitized) > 5000:
            sanitized = sanitized[:5000] + "... [TRUNCATED]"
        
        return sanitized.strip()
    
    def get_security_report(self, text: str) -> Dict[str, Any]:
        """
        Generate comprehensive security report for input text.
        
        Args:
            text: Input text to analyze
            
        Returns:
            Detailed security analysis report
        """
        scan_result = self.scan_input(text)
        
        return {
            "input_length": len(text),
            "is_safe": scan_result.is_safe,
            "risk_score": scan_result.risk_score,
            "threats_detected": scan_result.threats_detected,
            "recommended_action": scan_result.recommended_action,
            "sanitization_needed": scan_result.risk_score > 0.3,
            "analysis_timestamp": "2025-08-17T14:30:22Z",  # TODO: Use actual timestamp
            "detector_version": "1.0.0"
        }