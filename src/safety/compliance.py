"""
Compliance checker for GDPR, robots.txt, and other regulations.
"""

import re
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from urllib.parse import urlparse, urljoin
import structlog

logger = structlog.get_logger()

@dataclass
class ComplianceCheck:
    """Result of a compliance check."""
    compliant: bool
    issues: List[str]
    warnings: List[str]
    recommendations: List[str]

class ComplianceChecker:
    """Check compliance with various regulations and standards."""
    
    def __init__(self):
        """Initialize compliance checker."""
        self.pii_patterns = {
            'email': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            'phone': r'\b(?:\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}\b',
            'ssn': r'\b\d{3}-\d{2}-\d{4}\b',
            'credit_card': r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b'
        }
        
        logger.info("Compliance checker initialized")
    
    def check_gdpr_compliance(self, data: Dict[str, Any]) -> ComplianceCheck:
        """
        Check GDPR compliance for data processing.
        
        Args:
            data: Data to check for GDPR compliance
            
        Returns:
            ComplianceCheck result
        """
        issues = []
        warnings = []
        recommendations = []
        
        # Check for PII in data
        pii_found = self._detect_pii(str(data))
        if pii_found:
            issues.append(f"PII detected: {', '.join(pii_found.keys())}")
            recommendations.append("Implement PII masking or removal")
        
        # Check data minimization
        if len(str(data)) > 10000:  # Arbitrary threshold
            warnings.append("Large amount of data collected - ensure data minimization")
            recommendations.append("Review data collection to minimize personal data")
        
        # Check for consent indicators
        if 'consent' not in str(data).lower():
            warnings.append("No explicit consent indicators found")
            recommendations.append("Ensure proper consent mechanisms are in place")
        
        return ComplianceCheck(
            compliant=len(issues) == 0,
            issues=issues,
            warnings=warnings,
            recommendations=recommendations
        )
    
    def check_robots_compliance(self, url: str, user_agent: str = "ICA-Bot/1.0") -> ComplianceCheck:
        """
        Check robots.txt compliance for a URL.
        
        Args:
            url: URL to check
            user_agent: User agent string
            
        Returns:
            ComplianceCheck result
        """
        issues = []
        warnings = []
        recommendations = []
        
        try:
            parsed_url = urlparse(url)
            robots_url = urljoin(f"{parsed_url.scheme}://{parsed_url.netloc}", "/robots.txt")
            
            # TODO: Implement actual robots.txt fetching and parsing
            # For now, assume compliance
            recommendations.append(f"Verify robots.txt compliance for {robots_url}")
            
        except Exception as e:
            issues.append(f"Failed to check robots.txt: {str(e)}")
        
        return ComplianceCheck(
            compliant=len(issues) == 0,
            issues=issues,
            warnings=warnings,
            recommendations=recommendations
        )
    
    def _detect_pii(self, text: str) -> Dict[str, List[str]]:
        """
        Detect personally identifiable information in text.
        
        Args:
            text: Text to scan for PII
            
        Returns:
            Dictionary of PII types and matches found
        """
        pii_found = {}
        
        for pii_type, pattern in self.pii_patterns.items():
            matches = re.findall(pattern, text)
            if matches:
                pii_found[pii_type] = matches
        
        return pii_found
    
    def mask_pii(self, text: str) -> str:
        """
        Mask PII in text for safe storage/display.
        
        Args:
            text: Text containing potential PII
            
        Returns:
            Text with PII masked
        """
        masked_text = text
        
        # Mask emails
        masked_text = re.sub(
            self.pii_patterns['email'],
            lambda m: f"{m.group(0)[:3]}***@{m.group(0).split('@')[1]}",
            masked_text
        )
        
        # Mask phone numbers
        masked_text = re.sub(
            self.pii_patterns['phone'],
            "***-***-****",
            masked_text
        )
        
        # Mask SSNs
        masked_text = re.sub(
            self.pii_patterns['ssn'],
            "***-**-****",
            masked_text
        )
        
        # Mask credit cards
        masked_text = re.sub(
            self.pii_patterns['credit_card'],
            "****-****-****-****",
            masked_text
        )
        
        return masked_text