"""
Industry & Competitor Analysis App - Main FastAPI Application

This is the enhanced version of the starter app with full modular architecture,
comprehensive error handling, and production-ready features.
"""
from __future__ import annotations

import os
import time
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import <PERSON>AP<PERSON>, HTTPException, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import structlog

from src.api.routes import intent, research, screenshots, analyze, reports, health
from src.api.middleware import RateLimitMiddleware, LoggingMiddleware
from src.api.exceptions import setup_exception_handlers
from src.storage.database import init_database
from src.safety.compliance import ComplianceChecker

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management."""
    # Startup
    logger.info("Starting Industry & Competitor Analysis App")
    
    # Initialize database with migrations
    try:
        from src.storage.database import run_migrations
        await run_migrations()
    except Exception as e:
        logger.warning("Migration failed, falling back to direct table creation", error=str(e))
        await init_database()
    
    # Initialize compliance checker
    compliance_checker = ComplianceChecker()
    app.state.compliance_checker = compliance_checker
    
    logger.info("Application startup complete")
    
    yield
    
    # Shutdown
    logger.info("Shutting down application")

# Create FastAPI application
app = FastAPI(
    title="Industry & Competitor Analysis API",
    description="AI-powered competitive intelligence and market analysis platform",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan
)

# Add security middleware
app.add_middleware(
    TrustedHostMiddleware, 
    allowed_hosts=["localhost", "127.0.0.1", "*.example.com"]
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "https://app.example.com"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# Add custom middleware
app.add_middleware(LoggingMiddleware)
app.add_middleware(RateLimitMiddleware)

# Setup exception handlers
setup_exception_handlers(app)

# Include routers
app.include_router(health.router, prefix="/health", tags=["Health"])
app.include_router(intent.router, prefix="/intent", tags=["Intent Extraction"])
app.include_router(research.router, prefix="/research", tags=["Research"])
app.include_router(screenshots.router, prefix="/screenshots", tags=["Screenshots"])
app.include_router(analyze.router, prefix="/analyze", tags=["Analysis"])
app.include_router(reports.router, prefix="/reports", tags=["Reports"])

@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "name": "Industry & Competitor Analysis API",
        "version": "1.0.0",
        "description": "AI-powered competitive intelligence platform",
        "docs_url": "/docs",
        "health_url": "/health"
    }

@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """Add processing time header to responses."""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "src.app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_config={
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "default": {
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                },
            },
            "handlers": {
                "default": {
                    "formatter": "default",
                    "class": "logging.StreamHandler",
                    "stream": "ext://sys.stdout",
                },
            },
            "root": {
                "level": "INFO",
                "handlers": ["default"],
            },
        }
    )