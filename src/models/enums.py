"""
Enums for the Industry & Competitor Analysis App.

All enums used throughout the application for consistent data validation
and API contract enforcement.
"""

from enum import Enum

# Analysis Enums
class PrimaryGoal(str, Enum):
    market_entry_assessment = "market_entry_assessment"
    competitive_benchmark_feature = "competitive_benchmark_feature"
    competitive_benchmark_product = "competitive_benchmark_product"
    company_vs_competitors = "company_vs_competitors"
    feature_gap_recommendations = "feature_gap_recommendations"
    pricing_review = "pricing_review"
    positioning_messaging_review = "positioning_messaging_review"
    gtm_strategy_review = "gtm_strategy_review"
    market_size_forecast = "market_size_forecast"
    risk_scan_regulatory = "risk_scan_regulatory"

class OutputDepth(str, Enum):
    quick_scan = "quick_scan"
    deep_dive = "deep_dive"

class OutputFormat(str, Enum):
    markdown = "markdown"
    html = "html"
    pdf = "pdf"
    csv = "csv"
    json = "json"

# Business Enums
class ProductType(str, Enum):
    saas = "saas"
    on_prem = "on_prem"
    hardware_plus_software = "hardware_plus_software"
    services = "services"

class Deployment(str, Enum):
    cloud = "cloud"
    hybrid = "hybrid"
    on_prem = "on_prem"

class MarketPosition(str, Enum):
    leader = "leader"
    challenger = "challenger"
    niche = "niche"
    new_entrant = "new_entrant"
    unknown = "unknown"

class CompetitorType(str, Enum):
    direct = "direct"
    indirect = "indirect"
    substitute = "substitute"
    status_quo = "status_quo"

class SupportLevel(str, Enum):
    yes = "yes"
    partial = "partial"
    no = "no"
    na = "n/a"
    unknown = "unknown"

class Platform(str, Enum):
    web = "web"
    ios = "ios"
    android = "android"
    desktop = "desktop"
    api = "api"
    sdk = "sdk"

class ParityStrategy(str, Enum):
    parity = "parity"
    leapfrog = "leapfrog"
    selective_parity = "selective_parity"

# Pricing Enums
class PricingModel(str, Enum):
    seat = "seat"
    usage = "usage"
    tiered = "tiered"
    flat = "flat"
    freemium = "freemium"
    open_source_dual = "open_source_dual"
    payg = "payg"
    perpetual_license = "perpetual_license"

class BillingCycle(str, Enum):
    monthly = "monthly"
    annual = "annual"
    one_time = "one_time"

class Unit(str, Enum):
    user = "user"
    seat = "seat"
    device = "device"
    event = "event"
    gb = "gb"
    workspace = "workspace"
    org = "org"
    unlimited = "unlimited"
    other = "other"

# GTM Enums
class Channel(str, Enum):
    plg = "plg"
    sales_led = "sales_led"
    partner_led = "partner_led"
    marketplace = "marketplace"
    field_sales = "field_sales"
    open_source = "open_source"

# Strategic Analysis Enums
class PorterForce(str, Enum):
    rivalry = "rivalry"
    threat_of_new_entrants = "threat_of_new_entrants"
    bargaining_power_buyers = "bargaining_power_buyers"
    bargaining_power_suppliers = "bargaining_power_suppliers"
    threat_of_substitutes = "threat_of_substitutes"

class ForceStrength(str, Enum):
    very_low = "very_low"
    low = "low"
    medium = "medium"
    high = "high"
    very_high = "very_high"

# Evidence and Analysis Enums
class SourceType(str, Enum):
    web = "web"
    pdf = "pdf"
    filing = "filing"
    pricing = "pricing"
    review = "review"
    documentation = "documentation"
    changelog = "changelog"
    press_release = "press_release"

class EntityType(str, Enum):
    company = "company"
    product = "product"
    feature = "feature"
    metric = "metric"
    price = "price"
    person = "person"
    location = "location"

class ClaimTopic(str, Enum):
    feature = "feature"
    pricing = "pricing"
    positioning = "positioning"
    metric = "metric"
    capability = "capability"
    limitation = "limitation"

class AnalysisStatus(str, Enum):
    pending = "pending"
    processing = "processing"
    completed = "completed"
    failed = "failed"
    cancelled = "cancelled"