"""
Common data models used across the application.
"""

from typing import Optional, Union, List
from datetime import datetime
from pydantic import BaseModel, Field

class Money(BaseModel):
    """Monetary amount with currency."""
    amount: float = Field(ge=0, description="Monetary amount")
    currency: str = Field(min_length=3, max_length=3, description="ISO currency code")
    
    def to_usd(self, exchange_rate: float = 1.0) -> 'Money':
        """Convert to USD using provided exchange rate."""
        return Money(amount=self.amount * exchange_rate, currency="USD")

class MoneyRange(BaseModel):
    """Range of monetary amounts."""
    min: Optional[Money] = None
    max: Optional[Money] = None

class Units(BaseModel):
    """Unit-based measurement."""
    units: int = Field(ge=0)
    unit_label: Optional[str] = None

# Union type for monetary or unit-based values
MoneyOrUnits = Union[Money, Units]

class TimeRange(BaseModel):
    """Time range specification."""
    start_date: Optional[str] = Field(default=None, description="YYYY-MM-DD")
    end_date: Optional[str] = Field(default=None, description="YYYY-MM-DD")
    as_of_date: Optional[str] = Field(default=None, description="YYYY-MM-DD")

class Viewport(BaseModel):
    """Browser viewport configuration."""
    width: int = 1440
    height: int = 900
    device_scale_factor: Optional[float] = None

class TargetURL(BaseModel):
    """Target URL for screenshot capture."""
    url: str
    label: Optional[str] = None
    selectors: List[str] = []
    login_required: Optional[bool] = None

class CrawlLimits(BaseModel):
    """Web crawling limits and policies."""
    max_pages_per_site: int = 5
    rate_limit_per_sec: float = 1.0
    respect_robots_txt: bool = True

class Timing(BaseModel):
    """Page loading timing configuration."""
    wait_until: str = Field(default="networkidle", description="load|domcontentloaded|networkidle")
    extra_wait_ms: int = 800