"""
Analysis Intent models - the core structured representation of user requirements.

Based on the comprehensive starter code with full Pydantic v2 models.
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, ConfigDict

from .enums import (
    PrimaryGoal, ProductType, Deployment, MarketPosition, CompetitorType,
    SupportLevel, Platform, ParityStrategy, PricingModel, BillingCycle,
    Unit, Channel, PorterForce, OutputFormat, OutputDepth
)
from .common import Money, MoneyRange, Units, MoneyOrUnits, TimeRange, Viewport, TargetURL, CrawlLimits, Timing

class BuyerType(BaseModel):
    """Target buyer persona."""
    name: Optional[str] = None
    org_size: Optional[str] = Field(default=None, description="startup|smb|midmarket|enterprise|public_sector")
    budget_range: Optional[MoneyRange] = None

class JTBDItem(BaseModel):
    """Jobs-to-be-Done item."""
    job: Optional[str] = None
    pains: List[str] = []
    desired_outcomes: List[str] = []

class MarketSize(BaseModel):
    """Market size information."""
    tam: Optional[MoneyOrUnits] = None
    sam: Optional[MoneyOrUnits] = None
    som: Optional[MoneyOrUnits] = None
    cagr_percent: Optional[float] = None
    cagr_years: Optional[int] = Field(default=None, ge=1)

class PESTEL(BaseModel):
    """PESTEL analysis factors."""
    political: List[str] = []
    economic: List[str] = []
    social: List[str] = []
    technological: List[str] = []
    environmental: List[str] = []
    legal: List[str] = []

class Market(BaseModel):
    """Market context and definition."""
    industry_name: str
    synonyms: List[str] = []
    subsector: Optional[str] = None
    geographies: List[str] = []
    time_range: Optional[TimeRange] = None
    market_size: Optional[MarketSize] = None
    buyer_types: List[BuyerType] = []
    jtbd: List[JTBDItem] = []
    pestel: Optional[PESTEL] = None
    porter_forces_focus: List[PorterForce] = []

class CompanyMetrics(BaseModel):
    """Company performance metrics."""
    arr: Optional[Money] = None
    users: Optional[int] = Field(default=None, ge=0)
    growth_rate_percent: Optional[float] = None
    retention_percent: Optional[float] = None
    nps: Optional[int] = Field(default=None, ge=-100, le=100)

class CompanyConstraints(BaseModel):
    """Company constraints and limitations."""
    team: Optional[str] = None
    budget: Optional[Money] = None
    timeline_weeks: Optional[int] = Field(default=None, ge=1)
    tech: Optional[str] = None
    regulatory: Optional[str] = None

class CompanyContext(BaseModel):
    """Company context and background."""
    company_name: str
    product_names: List[str] = []
    product_type: Optional[ProductType] = None
    deployment: Optional[Deployment] = None
    current_market_position: Optional[MarketPosition] = None
    uvp: Optional[str] = None
    metrics: Optional[CompanyMetrics] = None
    constraints: Optional[CompanyConstraints] = None

class Competition(BaseModel):
    """Competitive landscape definition."""
    competitor_list_explicit: List[str] = []
    competitor_types: List[CompetitorType] = []
    discovery_rules: List[str] = []
    exclusion_list: List[str] = []
    evidence_sources: List[str] = []

class FeatureDefinitions(BaseModel):
    """Feature analysis definitions."""
    support_levels: List[SupportLevel] = [SupportLevel.yes, SupportLevel.partial, SupportLevel.no, SupportLevel.unknown]
    notes: Optional[str] = None

class Capabilities(BaseModel):
    """Capability analysis requirements."""
    feature_taxonomy: List[str] = []
    feature_definitions: Optional[FeatureDefinitions] = None
    non_functional: List[str] = []
    platform_coverage: List[Platform] = []
    integrations: List[str] = []
    roadmap_horizon_months: Optional[int] = Field(default=None, ge=0)
    parity_strategy: Optional[ParityStrategy] = None

class PricingTier(BaseModel):
    """Pricing tier definition."""
    name: Optional[str] = None
    price: Optional[Money] = None
    billing_cycle: Optional[BillingCycle] = None
    unit: Optional[Unit] = None
    notes: Optional[str] = None

class Pricing(BaseModel):
    """Pricing analysis requirements."""
    pricing_models: List[PricingModel] = []
    tiers: List[PricingTier] = []
    discounting: Optional[str] = None

class GTM(BaseModel):
    """Go-to-market analysis requirements."""
    positioning_statement: Optional[str] = None
    personas: List[str] = []
    channels: List[Channel] = []
    messaging_themes: List[str] = []
    brand_considerations: Optional[str] = None

class EvidenceSignals(BaseModel):
    """Evidence signals to look for."""
    market: List[str] = []
    customers: List[str] = []
    demand: List[str] = []
    product: List[str] = []
    sales: List[str] = []
    support: List[str] = []
    risks: List[str] = []

class ConstraintsEthics(BaseModel):
    """Constraints and ethical considerations."""
    regulatory_scope: Optional[str] = None
    data_handling_prefs: Optional[str] = None
    region_language: Optional[str] = None

class Automation(BaseModel):
    """Automation and crawling configuration."""
    target_urls: List[TargetURL] = []
    viewport: Optional[Viewport] = None
    geolocation: Optional[str] = None
    crawl_limits: Optional[CrawlLimits] = None
    timing: Optional[Timing] = None
    file_naming: Optional[str] = None
    storage: Optional[str] = None
    redaction_rules: List[str] = []

class Advanced(BaseModel):
    """Advanced analysis configuration."""
    scoring_weights: Dict[str, float] = {}
    prioritization_framework: Optional[str] = None
    gap_analysis_targets: List[str] = []

class Outputs(BaseModel):
    """Output format and configuration."""
    format: Optional[OutputFormat] = OutputFormat.markdown
    depth: Optional[OutputDepth] = OutputDepth.deep_dive
    confidence_threshold: Optional[float] = Field(default=0.8, ge=0, le=1)

class Scope(BaseModel):
    """Analysis scope and objectives."""
    primary_goal: PrimaryGoal
    secondary_questions: List[str] = []
    decision_deadline: Optional[str] = None
    desired_outputs: List[str] = []

class AnalysisIntent(BaseModel):
    """
    Complete structured representation of analysis requirements.
    
    This is the core model that represents a user's analysis intent
    after being extracted from natural language prompts.
    """
    scope: Scope
    market: Market
    company_context: CompanyContext
    competition: Optional[Competition] = None
    capabilities: Optional[Capabilities] = None
    pricing: Optional[Pricing] = None
    gtm: Optional[GTM] = None
    evidence_signals: Optional[EvidenceSignals] = None
    constraints_ethics: Optional[ConstraintsEthics] = None
    automation: Optional[Automation] = None
    advanced: Optional[Advanced] = None
    outputs: Outputs

    model_config = ConfigDict(
        json_schema_extra={
            "examples": [
                {
                    "scope": {
                        "primary_goal": "competitive_benchmark_feature",
                        "secondary_questions": [
                            "Which security features are table stakes?",
                            "What do leaders gate behind higher tiers?"
                        ],
                        "desired_outputs": ["feature_matrix", "screenshot_pack"]
                    },
                    "market": {
                        "industry_name": "Electronic Lab Notebook (ELN)",
                        "subsector": "Bioprocess analytics",
                        "geographies": ["EU", "US"],
                        "time_range": {"as_of_date": "2025-08-17"},
                        "market_size": {"tam": {"amount": 1200, "currency": "USD"}, "cagr_percent": 9.2, "cagr_years": 5},
                        "jtbd": [{
                            "job": "Track experiments end-to-end",
                            "pains": ["manual data entry"],
                            "desired_outcomes": ["searchable records"]
                        }],
                        "porter_forces_focus": ["rivalry", "threat_of_new_entrants"]
                    },
                    "company_context": {
                        "company_name": "Laboperator",
                        "product_names": ["Workflow Orchestrator"],
                        "product_type": "saas",
                        "deployment": "cloud",
                        "current_market_position": "challenger",
                        "uvp": "Device orchestration + audit-ready workflows"
                    },
                    "competition": {
                        "competitor_list_explicit": ["Benchling", "IDBS", "RevLab"],
                        "competitor_types": ["direct", "indirect"],
                        "evidence_sources": ["vendor_sites", "pricing_pages", "docs", "g2"]
                    },
                    "capabilities": {
                        "feature_taxonomy": ["device_control", "audit_trails", "permissions", "integrations", "api"],
                        "platform_coverage": ["web", "api"],
                        "parity_strategy": "leapfrog"
                    },
                    "pricing": {
                        "pricing_models": ["seat", "usage"],
                        "tiers": [
                            {"name": "Team", "price": {"amount": 39, "currency": "USD"}, "billing_cycle": "monthly", "unit": "user"}
                        ]
                    },
                    "gtm": {"channels": ["plg", "sales_led"]},
                    "automation": {
                        "target_urls": [
                            {"url": "https://competitor.com/pricing", "label": "Pricing", "selectors": ["#pricing-table"]},
                            {"url": "https://competitor.com/changelog", "label": "Changelog"}
                        ],
                        "viewport": {"width": 1440, "height": 900},
                        "crawl_limits": {"max_pages_per_site": 10, "rate_limit_per_sec": 1.0, "respect_robots_txt": True},
                        "timing": {"wait_until": "networkidle", "extra_wait_ms": 800},
                        "file_naming": "{domain}_{label}_{ts}.png",
                        "storage": "s3://matt-product/screens"
                    },
                    "outputs": {"format": "markdown", "depth": "deep_dive", "confidence_threshold": 0.8}
                }
            ]
        }
    )