"""
Data models and schemas for the Industry & Competitor Analysis App.

This module contains all Pydantic models used throughout the application,
including the comprehensive AnalysisIntent schema from the starter code.
"""

from .analysis_intent import (
    AnalysisIntent,
    Scope,
    Market,
    CompanyContext,
    Competition,
    Capabilities,
    Pricing,
    GTM,
    EvidenceSignals,
    ConstraintsEthics,
    Automation,
    Advanced,
    Outputs
)

from .common import (
    Money,
    MoneyRange,
    Units,
    MoneyOrUnits,
    TimeRange,
    Viewport,
    TargetURL,
    CrawlLimits,
    Timing
)

from .enums import (
    PrimaryGoal,
    ProductType,
    Deployment,
    MarketPosition,
    CompetitorType,
    SupportLevel,
    Platform,
    ParityStrategy,
    PricingModel,
    BillingCycle,
    Unit,
    Channel,
    PorterForce,
    OutputFormat,
    OutputDepth
)

__all__ = [
    # Core analysis models
    "AnalysisIntent",
    "Scope",
    "Market", 
    "CompanyContext",
    "Competition",
    "Capabilities",
    "Pricing",
    "GTM",
    "EvidenceSignals",
    "ConstraintsEthics",
    "Automation",
    "Advanced",
    "Outputs",
    
    # Common models
    "Money",
    "MoneyRange",
    "Units",
    "MoneyOrUnits",
    "TimeRange",
    "Viewport",
    "TargetURL",
    "CrawlLimits",
    "Timing",
    
    # Enums
    "PrimaryGoal",
    "ProductType",
    "Deployment",
    "MarketPosition",
    "CompetitorType",
    "SupportLevel",
    "Platform",
    "ParityStrategy",
    "PricingModel",
    "BillingCycle",
    "Unit",
    "Channel",
    "PorterForce",
    "OutputFormat",
    "OutputDepth"
]