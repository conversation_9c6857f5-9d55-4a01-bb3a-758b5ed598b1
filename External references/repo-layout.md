Boom — your ready-to-run repo is packed and good to go:

**[Download the repo (ZIP)](sandbox:/mnt/data/kiro-ica-app.zip)**

### What you’re getting

* A clean FastAPI service with modular packages (LLM intent extraction, planning, research, screenshots, basic analysis, and HTML report).
* Pydantic v2 models for the full intent schema (JSON Schema via `model_json_schema()`), wired into **OpenAI Structured Outputs** strict mode. ([Pydantic][1], [OpenAI Platform][2], [OpenAI][3])
* Research pipeline using **duckduckgo-search** → **Trafilatura** for readable text → LLM synthesis. ([PyPI][4], [trafilatura.readthedocs.io][5])
* **Playwright** screenshotter with full-page and element clips. ([Playwright][6])
* Dev DX: pytest test, GitHub Actions CI, Dockerfile, and a small safety guard. (Dockerfile set up for Chromium + Playwright.)
* Clear README with quickstart.

### Quickstart (recap)

```bash
python -m venv .venv && source .venv/bin/activate
pip install -r requirements.txt
playwright install
export OPENAI_API_KEY=sk-...
uvicorn src.app:app --reload
# visit http://127.0.0.1:8000/docs
```

FastAPI docs & tutorial for reference. ([FastAPI][7])

### How Kiro should evolve this (next commits)

1. **Tighten Structured Outputs everywhere**
   Use JSON Schema for feature tagging and pricing parsers too (not just intent). Pydantic v2 makes schema generation easy. ([Pydantic][1])
2. **Evidence depth + PDF robustness**
   Add PyMuPDF/pdfplumber passes for filings, brochures, and pricing PDFs; prefer PyMuPDF for general text and table recipes. ([PyMuPDF][8], [GitHub][9])
3. **Screenshot polish**
   Expose viewport/locale/timezone and selector-masking in the API; stick with Playwright’s `full_page=True` and element `locator.screenshot()`. ([Playwright][6])
4. **Respect robots/TOS by default**
   Fetch + parse `/robots.txt` per host and throttle; the scaffold includes a minimal helper—make it stricter. (See robots best-practice discussions/RFC.) ([Stack Overflow][10])
5. **Observability**
   Add OpenTelemetry FastAPI instrumentation + structlog JSON logs across LLM calls and page fetches. ([DevDocs][11])
6. **Docs & links inside the app**
   Link docstrings and README sections to upstream docs (Trafilatura usage, Playwright screenshots) for maintainers. ([trafilatura.readthedocs.io][5], [Playwright][6])


[1]: https://docs.pydantic.dev/latest/concepts/json_schema/?utm_source=chatgpt.com "JSON Schema"
[2]: https://platform.openai.com/docs/guides/structured-outputs?utm_source=chatgpt.com "Structured model outputs - OpenAI API"
[3]: https://openai.com/index/introducing-structured-outputs-in-the-api/?utm_source=chatgpt.com "Introducing Structured Outputs in the API"
[4]: https://pypi.org/project/duckduckgo-search/?utm_source=chatgpt.com "duckduckgo-search"
[5]: https://trafilatura.readthedocs.io/en/latest/usage-python.html?utm_source=chatgpt.com "With Python — Trafilatura 2.0.0 documentation - Read the Docs"
[6]: https://playwright.dev/python/docs/screenshots?utm_source=chatgpt.com "Screenshots | Playwright Python"
[7]: https://fastapi.tiangolo.com/?utm_source=chatgpt.com "FastAPI"
[8]: https://pymupdf.readthedocs.io/en/latest/recipes.html?utm_source=chatgpt.com "How to Guide - PyMuPDF 1.26.3 documentation"
[9]: https://github.com/jsvine/pdfplumber?utm_source=chatgpt.com "jsvine/pdfplumber - and easily extract text and tables."
[10]: https://stackoverflow.com/questions/77365685/how-to-take-fullpage-screenshot-using-playwright?utm_source=chatgpt.com "how to take fullpage screenshot using playwright"
[11]: https://devdocs.io/fastapi/?utm_source=chatgpt.com "FastAPI documentation"