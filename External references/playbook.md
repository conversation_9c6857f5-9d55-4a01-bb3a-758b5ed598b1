awesome—here’s a practical, build-ready playbook <PERSON><PERSON> can follow to create your “Industry & Competitor Analysis” app, plus a comprehensive improvements backlog.

---

# Build Playbook (for Kiro)

## 0) Macro architecture (modules & data flow)

**Flow:** User prompt → Intent → Requirements (typed) → Research Plan → Evidence harvesting (crawl + screenshots) → Extraction & scoring → Synthesis (with citations) → Report & artifacts (tables, charts, PDFs) → Review loop.

**Core modules**

1. **Intent → Requirements**

   * LLM with **Structured Outputs** (JSON Schema) to normalize: industry, time range, geos, competitors, features, metrics, market position, ICP, segment, channels, pricing, compliance, etc. Use Pydantic for validation. ([OpenAI Platform][1], [Pydantic][2])
2. **Research Planner**

   * Turns requirements into a search & source plan (news, docs, pricing pages, review sites, app stores, filings).
3. **Web Harvester**

   * Playwright (preferred) for dynamic pages + screenshots (mask PII/UI personal areas); optional Selenium fallback. ([Playwright][3], [Selenium][4])
   * Respect **robots.txt**/site TOS; don’t bypass protections; throttle; identify UA. (ROBOTS RFC 9309 + ongoing AI-crawl signals work). ([RFC Editor][5], [IETF][6])
4. **Content Extractor**

   * Trafilatura for clean HTML text; PyMuPDF/pdfplumber for PDFs/tables. ([doc.courtbouillon.org][7], [pymupdf.readthedocs.io][8], [GitHub][9])
5. **Evidence Store**

   * SQLite/Postgres with tables for `documents`, `entities`, `claims`, `citations`, `screenshots`.
6. **Analyzer**

   * LLM graders/structured outputs to label features, pricing, positioning, moats; compute diffs vs. competitors; generate feature matrix & pricing comparators; produce Porter/PESTEL/JTBD summaries with citations. ([OpenAI Platform][10], [Harvard Business Review][11], [Oxford Reference][12])
7. **Reporter**

   * FastAPI endpoints + export to HTML/PDF (WeasyPrint or ReportLab); figures via matplotlib.
8. **Observability & Ops**

   * Structured logging (structlog) & traces (OpenTelemetry) + CI/CD with GitHub Actions; containerize with Docker best practices. ([structlog][13], [opentelemetry-python-contrib.readthedocs.io][14], [GitHub Docs][15], [Docker Documentation][16])
9. **Safety & Compliance**

   * GDPR principles (lawfulness, minimization), and OWASP LLM Top-10 (prompt-injection defenses). ([EUR-Lex][17], [GDPR][18], [OWASP Gen AI Security Project][19])

---

## 1) Tech stack choices

* **API:** FastAPI + Pydantic v2 for models/validation. ([FastAPI][20], [Pydantic][2])
* **LLM orchestration:** OpenAI **Responses API** with **Structured Outputs** JSON Schema; optional tool/function calling for URL fetching/summarization steps. ([OpenAI Platform][21])
* **Browser automation:** Playwright Python (full-page & element screenshots, `mask` locators, mobile/geo/timezone emulation). Selenium only if required. ([Playwright][3])
* **Extraction:** Trafilatura (HTML), PyMuPDF/pdfplumber (PDF text/tables). ([doc.courtbouillon.org][7], [pymupdf.readthedocs.io][8], [GitHub][9])
* **Data sources (examples):**

  * Company filings & KPIs: SEC EDGAR Company Facts API. ([OpenAI Platform][1])
  * Search: duckduckgo-search (or your preferred compliant search tool). ([GitHub][22])
* **Observability:** structlog + OpenTelemetry FastAPI instrumentation. ([structlog][13], [opentelemetry-python-contrib.readthedocs.io][14])
* **CI/CD & container:** GitHub Actions; Dockerfile best practices. ([GitHub Docs][15], [Docker Documentation][16])

---

## 2) Data contracts (Pydantic + JSON Schema)

### 2.1 Requirements schema (Structured Output)

Use OpenAI Structured Outputs to **guarantee** JSON matching this schema (strict mode), then validate with Pydantic.

```json
{
  "type": "object",
  "properties": {
    "industry": {"type": "string"},
    "subindustry": {"type": "string"},
    "time_range": {
      "type": "object",
      "properties": {"from": {"type": "string"}, "to": {"type": "string"}}
    },
    "geographies": {"type": "array", "items": {"type": "string"}},
    "target_segments": {"type": "array", "items": {"type": "string"}},
    "competitors": {"type": "array", "items": {"type": "string"}},
    "features": {"type": "array", "items": {"type": "string"}},
    "pricing_focus": {"type": "boolean"},
    "metrics": {"type": "array", "items": {"type": "string"}},
    "positioning_questions": {"type": "array", "items": {"type": "string"}},
    "icp": {"type": "string"},
    "channels": {"type": "array", "items": {"type": "string"}},
    "compliance": {"type": "array", "items": {"type": "string"}},
    "report_format": {"type": "string", "enum": ["brief", "standard", "deep"]},
    "evidence_depth": {"type": "string", "enum": ["light", "standard", "heavy"]}
  },
  "required": ["industry", "time_range"]
}
```

(Use **Structured Outputs** with the Responses API to enforce this schema.) ([OpenAI Platform][1])

### 2.2 Evidence document

```json
{
  "url": "string",
  "source_type": "web|pdf|filing|pricing|review",
  "title": "string",
  "published_at": "ISO8601",
  "scraped_at": "ISO8601",
  "text": "string",
  "lang": "string",
  "entities": [{"type":"company|feature|metric","text":"string"}],
  "hash": "sha256"
}
```

### 2.3 Finding / claim

```json
{
  "claim_id": "uuid",
  "topic": "feature|pricing|positioning|metric",
  "target": "company|product|feature",
  "value": "string|number",
  "confidence": 0.0,
  "evidence": [{"url":"...", "quote":"...", "offset": [start,end]}]
}
```

---

## 3) Orchestration blueprint

1. **Intent capture → Requirements**

   * Prompt the model with examples and the JSON Schema above, **strict** mode. Reject or ask for a follow-up only when required fields are missing. ([OpenAI Platform][1])
2. **Research planning**

   * Expand competitor list (seed from user + discovery via search query templates).
   * Build a **source plan**: pricing pages, product pages, docs/changelogs, trust centers, app store listings, review aggregators, press/news, filings.
3. **Harvest**

   * Fetch pages, respect robots, backoff, concurrency caps; for protected/JS pages use Playwright; save **screenshots** (`full_page=True`, `mask=[locators]`). ([RFC Editor][5], [Playwright][3])
4. **Extract**

   * HTML: Trafilatura for readability-grade text. PDFs: PyMuPDF (text + table detection) or pdfplumber (tables). ([doc.courtbouillon.org][7], [pymupdf.readthedocs.io][23], [GitHub][9])
5. **Analyze**

   * Run LLM graders to:

     * Tag features & map to your taxonomy.
     * Parse pricing tiers & normalize currency.
     * Build **Porter Five Forces**/**PESTEL**/**JTBD** summaries for the specified industry/time/geo. ([Harvard Business Review][11], [Oxford Reference][12])
6. **Compare & score**

   * Feature matrix by competitor (coverage, depth, quality signals).
   * Pricing comparator (per-seat, per-usage, hidden fees).
7. **Synthesize**

   * Draft insights with **inline citations** (URL + date) and attach **screenshot evidence** for key claims.
8. **Deliver**

   * FastAPI endpoints returning JSON, plus HTML/PDF “brief / standard / deep” reports.
9. **Log & trace**

   * structlog JSON logs; OpenTelemetry traces on each job to measure latency and failures. ([structlog][13], [opentelemetry-python-contrib.readthedocs.io][14])

---

## 4) Kiro’s step-by-step tasks

### A) Project skeleton

* Init repo with `src/` packages: `api`, `llm`, `planner`, `crawler`, `extract`, `analyze`, `report`, `storage`, `safety`.
* Add FastAPI app, health route, and `/analyze` POST (accepts raw text). ([FastAPI][20])
* Define Pydantic v2 models mirroring the JSON Schemas. ([Pydantic][2])

### B) Intent → Requirements

* Implement `llm/requirements.py`:

  * Call OpenAI **Responses API** with **Structured Outputs** (strict), pass the Requirements schema above. Validate via Pydantic, return typed object. ([OpenAI Platform][21])

### C) Research planning

* `planner/plan.py`: Given requirements, produce source queries (DDG, site: filters), and URL patterns per competitor (pricing, docs, changelog). (Use duckduckgo-search lib as default.) ([GitHub][22])

### D) Harvesting & screenshots

* `crawler/playwright_runner.py`:

  * Headless Chromium; emulate locale/timezone; use `page.screenshot(full_page=True, mask=[...])` for PII-prone selectors (e.g., `#user-avatar`, `.email`). ([Playwright][24])
  * Respect robots (fetch and parse `/robots.txt` once per host; obey disallow; throttle). ([RFC Editor][5])
  * Save `{url, status, html, screenshot_path}`.

### E) Extraction

* `extract/html.py`: Trafilatura `extract` to main text; keep title/meta. ([doc.courtbouillon.org][7])
* `extract/pdf.py`: PyMuPDF text + `find_tables()` (or pdfplumber `.extract_table()`), store tables as rows. ([pymupdf.readthedocs.io][23], [GitHub][9])

### F) Analysis

* `analyze/feature_mapper.py`: LLM **Structured Output** to map free-text to controlled feature taxonomy with confidence. ([OpenAI Platform][1])
* `analyze/pricing.py`: Extract tier names, bullets, prices; normalize; compute effective seat price.
* `analyze/frameworks.py`:

  * **Porter Five Forces** summary, **PESTEL** highlights, **JTBD** job map based on evidence. ([Harvard Business Review][11], [Oxford Reference][12])
* Produce a feature matrix and competitor scorecards.

### G) Reporting

* `report/html.py`: Compose HTML with tables, inline citations, and screenshot thumbnails.
* `report/pdf.py`: Convert HTML→PDF (WeasyPrint) for export.
* API routes: `/analyze` (start job), `/reports/{id}` (fetch JSON/HTML/PDF).

### H) Observability & CI

* Add structlog JSON logger & OTel FastAPI instrumentation. ([structlog][13], [opentelemetry-python-contrib.readthedocs.io][14])
* GitHub Actions workflow: run lint/tests, build Docker image, push artifact. ([GitHub Docs][15])
* Dockerfile with multi-stage build, `.dockerignore`, non-root user. ([Docker Documentation][16])

### I) Compliance guardrails

* Robots/TOS checks pre-crawl; configurable allowlist. ([RFC Editor][5])
* GDPR: don’t store personal data unless necessary; mask, minimize, delete on request; document legal basis. ([EUR-Lex][17], [GDPR][18])
* LLM security: prompt-injection checks & output handling per **OWASP LLM Top-10**. ([OWASP Gen AI Security Project][19])

---

## 5) Prompts that work (summaries)

* **Requirements extraction (Structured Output):**
  “You are a strategy analyst. Extract key requirements from the user text. Use the provided JSON Schema. Only output valid JSON. If a field is unknown, omit it rather than guessing.” (Use **Structured Outputs strict**.) ([OpenAI Platform][1])

* **Feature tagging:**
  “From the evidence text, map phrases to our feature taxonomy (list provided). Return for each: `feature_key`, `evidence_quote`, `url`, `confidence` (0–1).”

* **Pricing parser:**
  “Extract tiers, monthly/annual prices, per-seat / per-usage units, overages, feature gates. Return normalized currency (ISO-4217) and period.”

* **Framework generators:**
  “Create a Porter Five Forces summary for {industry, geo, time}. Cite lines from evidence. Avoid unsupported claims.” ([Harvard Business Review][11])
  “Summarize PESTEL factors observed in the evidence (top 3 per letter).” ([Oxford Reference][12])
  “Build a JTBD job map (main job + steps) from customer reviews/news.” ([Harvard Business Review][25])

---

## 6) Acceptance criteria (MVP)

* 95%+ of user prompts yield **valid** Requirements JSON (schema-valid, Pydantic-valid). ([OpenAI Platform][1], [Pydantic][2])
* Given a sample of 10 competitor pricing pages, system returns price + tier names + plan gates with citations & at least one screenshot per page. ([Playwright][3])
* Feature matrix shows ≥80% of requested features mapped for top 5 competitors with confidence scores.
* Reports include sources list (URL + accessed date) and embedded screenshot thumbnails.
* Crawling respects robots and throttling by default. ([RFC Editor][5])
* Logs/traces present for each step; CI builds & tests on PR. ([opentelemetry-python-contrib.readthedocs.io][14], [GitHub Docs][15])

---

# Comprehensive Improvements Backlog

### A) Accuracy & Reliability

* **Dual-pass verification:** Have a second LLM “grader” verify each claim (semantic match to evidence; flag hallucinations). ([OpenAI Platform][10])
* **Schema everywhere:** Use Structured Outputs for *all* intermediate steps (feature lists, pricing, claims) to eliminate parsing errors. ([OpenAI Platform][1])
* **Date discipline:** Normalize publish dates; reject stale evidence outside `time_range`.
* **PDF robustness:** Prefer PyMuPDF `find_tables()`; fallback to pdfplumber; attach OCR only if needed. ([pymupdf.readthedocs.io][23], [GitHub][9])

### B) Coverage & Depth

* **Connectors:** SEC EDGAR (K-IQ), investor relations PDFs, product changelogs, status pages. ([OpenAI Platform][1])
* **App stores & reviews:** Summarize trends; extract pros/cons into JTBD “struggles.” ([Harvard Business Review][25])
* **News cadence:** Freshness passes (last 30/90 days) for momentum signals.

### C) Evidence quality

* **Screenshot QA:** Use Playwright `mask` for PII and `locator.screenshot()` for component-level captures. ([Playwright][26])
* **Dedup/near-dup:** Hash content; cluster by URL/host; keep one canonical copy.
* **Citation strictness:** Every insight links to at least one source + line-level quote.

### D) Analysis features

* **Framework pack:** One-click **Porter**, **PESTEL**, **JTBD** sections with side-by-side competitor deltas. ([Harvard Business Review][11], [Oxford Reference][12])
* **Pricing tear-downs:** Detect effective price/seat, usage caps, overages, and feature gates; currency normalization (ECB rates). ([ECB Data Portal][27])
* **Momentum indicators:** Release cadence (changelog deltas), hiring (careers pages), funding (where permitted).

### E) UX & Output

* **Interactive matrix:** Sort/filter features by competitor; click to open evidence screenshot.
* **Explainability:** Hover-to-reveal quote + URL for each cell.
* **Export pack:** HTML, PDF, CSV (matrix), ZIP of screenshots.

### F) Performance & Cost

* **Batching & caching:** Cache page fetches; only refresh outside TTL; parallel Playwright contexts with polite concurrency.
* **Adaptive depth:** “Brief / standard / deep” controls crawl breadth & LLM tokens.

### G) Ops & Safety

* **Observability:** Traces across LLM+network; span attributes: tokens, latency, cache hits. ([opentelemetry-python-contrib.readthedocs.io][14])
* **LLM security (OWASP):**

  * Prompt-injection scanner for retrieved text; strip/neutralize “ignore instructions” strings; sandbox tool outputs; rate-limit & size-limit inputs. ([OWASP Gen AI Security Project][19])
* **Compliance:** GDPR data minimization and clear legal bases for any personal data; configurable retention. ([GDPR][18])
* **Robots/TOS:** Crawl only where permitted; optional “manual URL” mode for restricted sites. ([RFC Editor][5])

### H) Developer experience

* **CI:** Lint/type-check/tests; Playwright smoke tests in CI; Docker build & scan. ([GitHub Docs][15], [Docker Documentation][16])
* **Test fixtures:** Golden pages & PDFs for deterministic extraction; pytest markers for networked vs. offline tests. ([docs.pytest.org][28])

---

## Notes on key choices (why these)

* **Structured Outputs** ensures zero-shot JSON is always schema-correct—ideal for translating messy human prompts into reliable requirements and later for feature/pricing extraction. ([OpenAI Platform][1])
* **Playwright** provides reliable full-page and element screenshots with masking and emulation—very handy when you need reproducible, PII-safe evidence. ([Playwright][3])
* **Respect for robots/GDPR** is non-negotiable: keep you compliant and reputable by default. ([RFC Editor][5], [EUR-Lex][17])
* **Porter/PESTEL/JTBD** are the right analysis frames for strategy + feature decisions; the app can automate first-drafts from evidence, then you refine. ([Harvard Business Review][11], [Oxford Reference][12])

---

[1]: https://platform.openai.com/docs/guides/structured-outputs?utm_source=chatgpt.com "Structured model outputs - OpenAI API"
[2]: https://docs.pydantic.dev/latest/?utm_source=chatgpt.com "Welcome to Pydantic - Pydantic"
[3]: https://playwright.dev/python/docs/screenshots?utm_source=chatgpt.com "Screenshots | Playwright Python"
[4]: https://www.selenium.dev/documentation/webdriver/waits/?utm_source=chatgpt.com "Waiting Strategies"
[5]: https://www.rfc-editor.org/info/rfc9309?utm_source=chatgpt.com "Information on RFC 9309"
[6]: https://www.ietf.org/blog/aipref-wg/?utm_source=chatgpt.com "IETF setting standards for AI preferences"
[7]: https://doc.courtbouillon.org/weasyprint/stable/api_reference.html?utm_source=chatgpt.com "API Reference - WeasyPrint 66.0 documentation - CourtBouillon"
[8]: https://pymupdf.readthedocs.io/?utm_source=chatgpt.com "PyMuPDF 1.26.3 documentation"
[9]: https://github.com/jsvine/pdfplumber?utm_source=chatgpt.com "jsvine/pdfplumber - and easily extract text and tables."
[10]: https://platform.openai.com/docs/guides/graders?utm_source=chatgpt.com "Graders - OpenAI API"
[11]: https://hbr.org/2008/01/the-five-competitive-forces-that-shape-strategy?utm_source=chatgpt.com "The Five Competitive Forces That Shape Strategy"
[12]: https://www.oxfordreference.com/abstract/10.1093/acref/9780199234899.001.0001/acref-9780199234899-e-4815?utm_source=chatgpt.com "PESTLE analysis"
[13]: https://www.structlog.org/?utm_source=chatgpt.com "structlog 25.4.0 documentation"
[14]: https://opentelemetry-python-contrib.readthedocs.io/en/latest/instrumentation/fastapi/fastapi.html?utm_source=chatgpt.com "OpenTelemetry FastAPI Instrumentation"
[15]: https://docs.github.com/actions/guides/building-and-testing-python?utm_source=chatgpt.com "Building and testing Python"
[16]: https://docs.docker.com/build/building/best-practices/?utm_source=chatgpt.com "Building best practices"
[17]: https://eur-lex.europa.eu/eli/reg/2016/679/oj/eng?utm_source=chatgpt.com "Regulation - 2016/679 - EN - gdpr - EUR-Lex - European Union"
[18]: https://gdpr-info.eu/art-5-gdpr/?utm_source=chatgpt.com "Art. 5 GDPR – Principles relating to processing of personal ..."
[19]: https://genai.owasp.org/llm-top-10/?utm_source=chatgpt.com "LLMRisks Archive - OWASP Gen AI Security Project"
[20]: https://fastapi.tiangolo.com/?utm_source=chatgpt.com "FastAPI"
[21]: https://platform.openai.com/docs/api-reference/responses?utm_source=chatgpt.com "API Reference"
[22]: https://github.com/adbar/trafilatura/blob/master/docs/usage-python.rst?utm_source=chatgpt.com "trafilatura/docs/usage-python.rst at master · adbar/trafilatura"
[23]: https://pymupdf.readthedocs.io/en/latest/recipes-text.html?utm_source=chatgpt.com "Text - PyMuPDF 1.26.3 documentation"
[24]: https://playwright.dev/python/docs/emulation?utm_source=chatgpt.com "Emulation | Playwright Python"
[25]: https://hbr.org/2016/09/know-your-customers-jobs-to-be-done?utm_source=chatgpt.com "Know Your Customers' “Jobs to Be Done”"
[26]: https://playwright.dev/python/docs/api/class-page?utm_source=chatgpt.com "Page | Playwright Python"
[27]: https://data.ecb.europa.eu/help/api/data?utm_source=chatgpt.com "API - ECB Data Portal"
[28]: https://docs.pytest.org/en/stable/contents.html?utm_source=chatgpt.com "Full pytest documentation"
