"""
app.py — One-file starter for:
1) Intent extraction (LLM → JSON matching schema)
2) Research scaffold (search + fetch + summarize)
3) Screenshot capture (Playwright)

Quick start
-----------
python -m venv .venv && source .venv/bin/activate
pip install -r requirements.txt
playwright install
export OPENAI_API_KEY=sk-...
uvicorn app:app --reload

Endpoints
---------
POST /extract-intent    {"prompt": "plain-English request"}
POST /research          AnalysisIntent JSON (from /extract-intent)
POST /screenshots       {"targets": [{"url": "https://...", "label": "Pricing", "selectors": ["#pricing"]}]}

Notes
-----
• Keep robots.txt and site TOS in mind when crawling.
• You can swap out search/fetch components (e.g., SerpAPI, Requests) while keeping the interface.
• This file is intentionally self-contained. In production, split into modules.
"""
from __future__ import annotations

import os
import json
import time
import hashlib
from enum import Enum
from typing import List, Optional, Union, Dict, Any

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field

# Optional but helpful utilities
from duckduckgo_search import DDGS  # lightweight web search
from trafilatura import fetch_url, extract  # robust content extraction

# OpenAI (official SDK >=1.0)
from openai import OpenAI

# Playwright (synchronous API)
from playwright.sync_api import sync_playwright

app = FastAPI(title="AI Market & Competitive Analysis Starter")

# -----------------------------
# Enums
# -----------------------------
class PrimaryGoal(str, Enum):
    market_entry_assessment = "market_entry_assessment"
    competitive_benchmark_feature = "competitive_benchmark_feature"
    competitive_benchmark_product = "competitive_benchmark_product"
    company_vs_competitors = "company_vs_competitors"
    feature_gap_recommendations = "feature_gap_recommendations"
    pricing_review = "pricing_review"
    positioning_messaging_review = "positioning_messaging_review"
    gtm_strategy_review = "gtm_strategy_review"
    market_size_forecast = "market_size_forecast"
    risk_scan_regulatory = "risk_scan_regulatory"

class ProductType(str, Enum):
    saas = "saas"
    on_prem = "on_prem"
    hardware_plus_software = "hardware_plus_software"
    services = "services"

class Deployment(str, Enum):
    cloud = "cloud"
    hybrid = "hybrid"
    on_prem = "on_prem"

class MarketPosition(str, Enum):
    leader = "leader"
    challenger = "challenger"
    niche = "niche"
    new_entrant = "new_entrant"
    unknown = "unknown"

class CompetitorType(str, Enum):
    direct = "direct"
    indirect = "indirect"
    substitute = "substitute"
    status_quo = "status_quo"

class SupportLevel(str, Enum):
    yes = "yes"
    partial = "partial"
    no = "no"
    na = "n/a"
    unknown = "unknown"

class Platform(str, Enum):
    web = "web"
    ios = "ios"
    android = "android"
    desktop = "desktop"
    api = "api"
    sdk = "sdk"

class ParityStrategy(str, Enum):
    parity = "parity"
    leapfrog = "leapfrog"
    selective_parity = "selective_parity"

class PricingModel(str, Enum):
    seat = "seat"
    usage = "usage"
    tiered = "tiered"
    flat = "flat"
    freemium = "freemium"
    open_source_dual = "open_source_dual"
    payg = "payg"
    perpetual_license = "perpetual_license"

class BillingCycle(str, Enum):
    monthly = "monthly"
    annual = "annual"
    one_time = "one_time"

class Unit(str, Enum):
    user = "user"
    seat = "seat"
    device = "device"
    event = "event"
    gb = "gb"
    workspace = "workspace"
    org = "org"
    unlimited = "unlimited"
    other = "other"

class Channel(str, Enum):
    plg = "plg"
    sales_led = "sales_led"
    partner_led = "partner_led"
    marketplace = "marketplace"
    field_sales = "field_sales"
    open_source = "open_source"

class PorterForce(str, Enum):
    rivalry = "rivalry"
    threat_of_new_entrants = "threat_of_new_entrants"
    bargaining_power_buyers = "bargaining_power_buyers"
    bargaining_power_suppliers = "bargaining_power_suppliers"
    threat_of_substitutes = "threat_of_substitutes"

class OutputFormat(str, Enum):
    markdown = "markdown"
    html = "html"
    pdf = "pdf"
    csv = "csv"
    jsonf = "json"  # avoid shadowing builtin json

class OutputDepth(str, Enum):
    quick_scan = "quick_scan"
    deep_dive = "deep_dive"

# -----------------------------
# Core models (Pydantic v2)
# -----------------------------
class Money(BaseModel):
    amount: float = Field(ge=0)
    currency: str = Field(min_length=3, max_length=3, description="ISO currency code")

class MoneyRange(BaseModel):
    min: Optional[Money] = None
    max: Optional[Money] = None

class Units(BaseModel):
    units: int = Field(ge=0)
    unit_label: Optional[str] = None

MoneyOrUnits = Union[Money, Units]

class TimeRange(BaseModel):
    start_date: Optional[str] = Field(default=None, description="YYYY-MM-DD")
    end_date: Optional[str] = Field(default=None, description="YYYY-MM-DD")
    as_of_date: Optional[str] = Field(default=None, description="YYYY-MM-DD")

class BuyerType(BaseModel):
    name: Optional[str] = None
    org_size: Optional[str] = Field(default=None, description="startup|smb|midmarket|enterprise|public_sector")
    budget_range: Optional[MoneyRange] = None

class JTBDItem(BaseModel):
    job: Optional[str] = None
    pains: List[str] = []
    desired_outcomes: List[str] = []

class MarketSize(BaseModel):
    tam: Optional[MoneyOrUnits] = None
    sam: Optional[MoneyOrUnits] = None
    som: Optional[MoneyOrUnits] = None
    cagr_percent: Optional[float] = None
    cagr_years: Optional[int] = Field(default=None, ge=1)

class PESTEL(BaseModel):
    political: List[str] = []
    economic: List[str] = []
    social: List[str] = []
    technological: List[str] = []
    environmental: List[str] = []
    legal: List[str] = []

class Market(BaseModel):
    industry_name: str
    synonyms: List[str] = []
    subsector: Optional[str] = None
    geographies: List[str] = []
    time_range: Optional[TimeRange] = None
    market_size: Optional[MarketSize] = None
    buyer_types: List[BuyerType] = []
    jtbd: List[JTBDItem] = []
    pestel: Optional[PESTEL] = None
    porter_forces_focus: List[PorterForce] = []

class CompanyMetrics(BaseModel):
    arr: Optional[Money] = None
    users: Optional[int] = Field(default=None, ge=0)
    growth_rate_percent: Optional[float] = None
    retention_percent: Optional[float] = None
    nps: Optional[int] = Field(default=None, ge=-100, le=100)

class CompanyConstraints(BaseModel):
    team: Optional[str] = None
    budget: Optional[Money] = None
    timeline_weeks: Optional[int] = Field(default=None, ge=1)
    tech: Optional[str] = None
    regulatory: Optional[str] = None

class CompanyContext(BaseModel):
    company_name: str
    product_names: List[str] = []
    product_type: Optional[ProductType] = None
    deployment: Optional[Deployment] = None
    current_market_position: Optional[MarketPosition] = None
    uvp: Optional[str] = None
    metrics: Optional[CompanyMetrics] = None
    constraints: Optional[CompanyConstraints] = None

class Competition(BaseModel):
    competitor_list_explicit: List[str] = []
    competitor_types: List[CompetitorType] = []
    discovery_rules: List[str] = []
    exclusion_list: List[str] = []
    evidence_sources: List[str] = []

class FeatureDefinitions(BaseModel):
    support_levels: List[SupportLevel] = [SupportLevel.yes, SupportLevel.partial, SupportLevel.no, SupportLevel.unknown]
    notes: Optional[str] = None

class Capabilities(BaseModel):
    feature_taxonomy: List[str] = []
    feature_definitions: Optional[FeatureDefinitions] = None
    non_functional: List[str] = []
    platform_coverage: List[Platform] = []
    integrations: List[str] = []
    roadmap_horizon_months: Optional[int] = Field(default=None, ge=0)
    parity_strategy: Optional[ParityStrategy] = None

class PricingTier(BaseModel):
    name: Optional[str] = None
    price: Optional[Money] = None
    billing_cycle: Optional[BillingCycle] = None
    unit: Optional[Unit] = None
    notes: Optional[str] = None

class Pricing(BaseModel):
    pricing_models: List[PricingModel] = []
    tiers: List[PricingTier] = []
    discounting: Optional[str] = None

class GTM(BaseModel):
    positioning_statement: Optional[str] = None
    personas: List[str] = []
    channels: List[Channel] = []
    messaging_themes: List[str] = []
    brand_considerations: Optional[str] = None

class EvidenceSignals(BaseModel):
    market: List[str] = []
    customers: List[str] = []
    demand: List[str] = []
    product: List[str] = []
    sales: List[str] = []
    support: List[str] = []
    risks: List[str] = []

class ConstraintsEthics(BaseModel):
    regulatory_scope: Optional[str] = None
    data_handling_prefs: Optional[str] = None
    region_language: Optional[str] = None

class TargetURL(BaseModel):
    url: str
    label: Optional[str] = None
    selectors: List[str] = []
    login_required: Optional[bool] = None

class Viewport(BaseModel):
    width: int = 1440
    height: int = 900
    device_scale_factor: Optional[float] = None

class CrawlLimits(BaseModel):
    max_pages_per_site: int = 5
    rate_limit_per_sec: float = 1.0
    respect_robots_txt: bool = True

class Timing(BaseModel):
    wait_until: str = Field(default="networkidle", description="load|domcontentloaded|networkidle")
    extra_wait_ms: int = 800

class Automation(BaseModel):
    target_urls: List[TargetURL] = []
    viewport: Optional[Viewport] = None
    geolocation: Optional[str] = None
    crawl_limits: Optional[CrawlLimits] = None
    timing: Optional[Timing] = None
    file_naming: Optional[str] = None
    storage: Optional[str] = None
    redaction_rules: List[str] = []

class Advanced(BaseModel):
    scoring_weights: Dict[str, float] = {}
    prioritization_framework: Optional[str] = None
    gap_analysis_targets: List[str] = []

class Outputs(BaseModel):
    format: Optional[OutputFormat] = OutputFormat.markdown
    depth: Optional[OutputDepth] = OutputDepth.deep_dive
    confidence_threshold: Optional[float] = Field(default=0.8, ge=0, le=1)

class Scope(BaseModel):
    primary_goal: PrimaryGoal
    secondary_questions: List[str] = []
    decision_deadline: Optional[str] = None
    desired_outputs: List[str] = []

class AnalysisIntent(BaseModel):
    scope: Scope
    market: Market
    company_context: CompanyContext
    competition: Optional[Competition] = None
    capabilities: Optional[Capabilities] = None
    pricing: Optional[Pricing] = None
    gtm: Optional[GTM] = None
    evidence_signals: Optional[EvidenceSignals] = None
    constraints_ethics: Optional[ConstraintsEthics] = None
    automation: Optional[Automation] = None
    advanced: Optional[Advanced] = None
    outputs: Outputs

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "scope": {
                        "primary_goal": "competitive_benchmark_feature",
                        "secondary_questions": [
                            "Which security features are table stakes?",
                            "What do leaders gate behind higher tiers?"
                        ],
                        "desired_outputs": ["feature_matrix", "screenshot_pack"]
                    },
                    "market": {
                        "industry_name": "Electronic Lab Notebook (ELN)",
                        "subsector": "Bioprocess analytics",
                        "geographies": ["EU", "US"],
                        "time_range": {"as_of_date": "2025-08-17"},
                        "market_size": {"tam": {"amount": 1200, "currency": "USD"}, "cagr_percent": 9.2, "cagr_years": 5},
                        "jtbd": [{
                            "job": "Track experiments end-to-end",
                            "pains": ["manual data entry"],
                            "desired_outcomes": ["searchable records"]
                        }],
                        "porter_forces_focus": ["rivalry", "threat_of_new_entrants"]
                    },
                    "company_context": {
                        "company_name": "Laboperator",
                        "product_names": ["Workflow Orchestrator"],
                        "product_type": "saas",
                        "deployment": "cloud",
                        "current_market_position": "challenger",
                        "uvp": "Device orchestration + audit-ready workflows"
                    },
                    "competition": {
                        "competitor_list_explicit": ["Benchling", "IDBS", "RevLab"],
                        "competitor_types": ["direct", "indirect"],
                        "evidence_sources": ["vendor_sites", "pricing_pages", "docs", "g2"]
                    },
                    "capabilities": {
                        "feature_taxonomy": ["device_control", "audit_trails", "permissions", "integrations", "api"],
                        "platform_coverage": ["web", "api"],
                        "parity_strategy": "leapfrog"
                    },
                    "pricing": {
                        "pricing_models": ["seat", "usage"],
                        "tiers": [
                            {"name": "Team", "price": {"amount": 39, "currency": "USD"}, "billing_cycle": "monthly", "unit": "user"}
                        ]
                    },
                    "gtm": {"channels": ["plg", "sales_led"]},
                    "automation": {
                        "target_urls": [
                            {"url": "https://competitor.com/pricing", "label": "Pricing", "selectors": ["#pricing-table"]},
                            {"url": "https://competitor.com/changelog", "label": "Changelog"}
                        ],
                        "viewport": {"width": 1440, "height": 900},
                        "crawl_limits": {"max_pages_per_site": 10, "rate_limit_per_sec": 1.0, "respect_robots_txt": true},
                        "timing": {"wait_until": "networkidle", "extra_wait_ms": 800},
                        "file_naming": "{domain}_{label}_{ts}.png",
                        "storage": "s3://matt-product/screens"
                    },
                    "outputs": {"format": "markdown", "depth": "deep_dive", "confidence_threshold": 0.8}
                }
            ]
        }
    }

# -----------------------------
# OpenAI helper
# -----------------------------
OPENAI_MODEL = os.getenv("OPENAI_MODEL", "gpt-4.1-mini")
client = OpenAI()

# NOTE: We embed the JSON Schema so the model must return structured output.
# You can tweak this at runtime if needed.
from typing import TypedDict

class JSONSchemaFormat(TypedDict):
    name: str
    schema: Dict[str, Any]
    strict: bool

# A compact version of the schema for strict outputs
INTENT_JSON_SCHEMA: Dict[str, Any] = AnalysisIntent.model_json_schema()

SYSTEM_INTENT = (
    "You are a structured data extractor. Output ONLY valid JSON that conforms exactly to the given schema. "
    "If unsure, set unknowns to empty arrays/strings and DO NOT invent facts."
)

USER_TEMPLATE = """
User message:
{prompt}

Developer notes:
- Map the user’s words to enums where possible.
- Infer geographies/time_range only if explicitly stated; otherwise leave blank.
- Do not add competitors not mentioned unless asked to discover.
- Keep feature_taxonomy concise; push extras to secondary_questions.
"""

# -----------------------------
# Utilities
# -----------------------------
def _safe_filename(template: str, **kwargs) -> str:
    try:
        return template.format(**kwargs)
    except Exception:
        # fallback to a safe unique name
        return f"shot_{int(time.time())}.png"

# -----------------------------
# Endpoints
# -----------------------------
class ExtractRequest(BaseModel):
    prompt: str

@app.post("/extract-intent", response_model=AnalysisIntent)
def extract_intent(req: ExtractRequest) -> AnalysisIntent:
    """Use OpenAI Structured Outputs to extract an AnalysisIntent from free text."""
    schema_format: Dict[str, Any] = {
        "type": "json_schema",
        "json_schema": {
            "name": "analysis_intent_schema",
            "schema": INTENT_JSON_SCHEMA,
            "strict": True,
        },
    }

    completion = client.responses.create(
        model=OPENAI_MODEL,
        messages=[
            {"role": "system", "content": SYSTEM_INTENT},
            {"role": "user", "content": USER_TEMPLATE.format(prompt=req.prompt)},
        ],
        response_format=schema_format,
    )

    # Extract JSON from the response
    try:
        content = completion.output[0].content[0].text  # Responses API text item
    except Exception:
        # fallback for older SDKs
        content = completion.choices[0].message["content"]  # type: ignore

    try:
        return AnalysisIntent.model_validate_json(content)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Invalid JSON from model: {e}")

# -----------------------------
# Research scaffold
# -----------------------------
class ResearchResponse(BaseModel):
    summary: str
    sources: List[str]

@app.post("/research", response_model=ResearchResponse)
def research(intent: AnalysisIntent) -> ResearchResponse:
    """
    Minimal research pipeline:
    1) Discover URLs via DuckDuckGo for each competitor/keyword.
    2) Fetch & extract readable text with Trafilatura.
    3) Summarize & synthesize with the OpenAI model.
    """
    queries = []
    ind = intent.market.industry_name
    competitors = (intent.competition.competitor_list_explicit if intent.competition else [])
    if competitors:
        for c in competitors:
            queries.append(f"{c} {ind} pricing")
            queries.append(f"{c} {ind} features")
            queries.append(f"{c} {ind} documentation")
    else:
        # generic discovery
        queries.append(f"top {ind} vendors pricing")
        queries.append(f"best {ind} platforms features")

    urls: List[str] = []
    with DDGS() as ddgs:
        for q in queries[:10]:
            for r in ddgs.text(q, max_results=5):
                url = r.get("href") or r.get("url")
                if url and url not in urls:
                    urls.append(url)

    # Fetch & extract main content
    docs: List[Dict[str, str]] = []
    for u in urls[:15]:
        try:
            html = fetch_url(u)
            if not html:
                continue
            text = extract(html, url=u, favor_precision=True) or ""
            if len(text) < 500:
                continue
            docs.append({"url": u, "text": text[:8000]})  # truncate per-token limits
        except Exception:
            continue

    if not docs:
        raise HTTPException(status_code=404, detail="No sources found/extracted.")

    # Summarize with model
    synth_prompt = (
        "You are a rigorous market & competitive analyst. Synthesize the findings from the following sources. "
        "Provide a concise summary with pricing patterns, notable differentiators, and feature themes. "
        "Cite sources inline as [n] and include the URL list at the end."
    )

    # Build a compact context window
    context_blocks = []
    for i, d in enumerate(docs, start=1):
        context_blocks.append(f"SOURCE [{i}] {d['url']}\n{d['text']}\n")
    context_text = "\n\n".join(context_blocks[:8])  # cap sources for brevity

    completion = client.responses.create(
        model=OPENAI_MODEL,
        input=[
            {"role": "system", "content": synth_prompt},
            {"role": "user", "content": f"Industry: {ind}\n\n{context_text}"},
        ],
    )

    try:
        summary = completion.output[0].content[0].text
    except Exception:
        summary = completion.choices[0].message["content"]  # type: ignore

    return ResearchResponse(summary=summary, sources=[d["url"] for d in docs])

# -----------------------------
# Screenshot capture
# -----------------------------
class ScreenshotTarget(BaseModel):
    url: str
    label: Optional[str] = None
    selectors: List[str] = []

class ScreenshotRequest(BaseModel):
    targets: List[ScreenshotTarget]
    out_dir: str = Field(default="screens")
    viewport: Optional[Viewport] = None
    wait_until: str = Field(default="networkidle")
    extra_wait_ms: int = Field(default=800, ge=0)

class ScreenshotResult(BaseModel):
    saved: List[str]

@app.post("/screenshots", response_model=ScreenshotResult)
def screenshots(req: ScreenshotRequest) -> ScreenshotResult:
    vp = req.viewport or Viewport()
    os.makedirs(req.out_dir, exist_ok=True)

    saved_paths: List[str] = []
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=True)
        context = browser.new_context(viewport={"width": vp.width, "height": vp.height})
        page = context.new_page()

        for t in req.targets:
            page.goto(t.url, wait_until=req.wait_until)
            if req.extra_wait_ms:
                page.wait_for_timeout(req.extra_wait_ms)
            safe_label = (t.label or t.url.split("//")[-1].split("/")[0]).replace(" ", "_")
            ts = time.strftime("%Y%m%d-%H%M%S")
            full_path = os.path.join(req.out_dir, f"{safe_label}_{ts}_full.png")
            page.screenshot(path=full_path, full_page=True)
            saved_paths.append(full_path)
            for sel in (t.selectors or []):
                el = page.locator(sel).first
                if el.count() > 0:
                    h = hashlib.sha1(sel.encode()).hexdigest()[:8]
                    sel_path = os.path.join(req.out_dir, f"{safe_label}_{h}_{ts}.png")
                    el.screenshot(path=sel_path)
                    saved_paths.append(sel_path)

        context.close(); browser.close()

    return ScreenshotResult(saved=saved_paths)

# -----------------------------
# requirements.txt (for convenience when copy/pasting)
# -----------------------------
REQUIREMENTS_TXT = """
fastapi>=0.110
uvicorn>=0.29
pydantic>=2.6
openai>=1.30
playwright>=1.45
duckduckgo-search>=8.1.1
trafilatura>=2.0.0
"""

@app.get("/requirements.txt")
def requirements_text() -> str:
    return REQUIREMENTS_TXT

# -----------------------------
# Run helper
# -----------------------------
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app:app", host="0.0.0.0", port=8000, reload=True)
