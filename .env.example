# Industry & Competitor Analysis App - Environment Configuration

# OpenRouter Configuration (Required)
OPENROUTER_API_KEY=sk-or-v1-your-openrouter-api-key-here
OPENROUTER_MODEL=openai/gpt-4o-mini
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_SITE_URL=https://your-site.com
OPENROUTER_APP_NAME=Industry-Competitor-Analysis

# Database Configuration
DATABASE_URL=sqlite:///./ica_app.db
# For PostgreSQL: postgresql://user:password@localhost/ica_db

# Redis Configuration (Optional)
REDIS_URL=redis://localhost:6379/0

# Application Configuration
LOG_LEVEL=INFO
DEBUG=false
ENVIRONMENT=development

# Security Configuration
SECRET_KEY=your-secret-key-here-change-in-production
ALLOWED_HOSTS=localhost,127.0.0.1

# Rate Limiting Configuration
RATE_LIMIT_ENABLED=true
DEFAULT_RATE_LIMIT=60

# Monitoring Configuration (Optional)
SENTRY_DSN=
OTEL_EXPORTER_OTLP_ENDPOINT=

# Playwright Configuration
PLAYWRIGHT_HEADLESS=true
PLAYWRIGHT_TIMEOUT=30000

# Development Tools
SQL_ECHO=false
RELOAD=true