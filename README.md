# Industry & Competitor Analysis App

AI-powered competitive intelligence and market analysis platform that transforms natural language requests into comprehensive competitive analysis reports.

## 🚀 Features

- **Intent Extraction**: Convert natural language prompts to structured analysis requirements using OpenAI Structured Outputs
- **Automated Research**: Gather evidence from web sources, pricing pages, and competitor materials
- **Strategic Analysis**: Generate Porter Five Forces, PESTEL, and Jobs-to-be-Done frameworks
- **Visual Evidence**: Capture screenshots of competitor interfaces and key materials
- **Comprehensive Reports**: Multi-format reports (HTML, PDF, CSV) with citations and evidence

## 🏗️ Architecture

The application follows a modular architecture with the following components:

- **API Layer** (`src/api/`): FastAPI routes and middleware
- **Models** (`src/models/`): Pydantic data models and schemas
- **LLM Integration** (`src/llm/`): OpenAI Structured Outputs integration
- **Storage** (`src/storage/`): Database and data persistence
- **Safety** (`src/safety/`): Security and compliance features

## 📋 Prerequisites

- Python 3.11+
- OpenAI API key
- Playwright browsers

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd industry-competitor-analysis
   ```

2. **Create virtual environment**
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Install Playwright browsers**
   ```bash
   playwright install
   ```

5. **Set environment variables**
   ```bash
   export OPENROUTER_API_KEY=sk-or-v1-your-openrouter-api-key
   export OPENROUTER_MODEL=openai/gpt-4o-mini  # Optional, defaults to gpt-4o-mini
   export DATABASE_URL=sqlite:///./ica_app.db  # Optional, defaults to SQLite
   ```

## 🚀 Quick Start

1. **Start the development server**
   ```bash
   uvicorn src.app:app --reload
   ```

2. **Access the API documentation**
   - Swagger UI: http://localhost:8000/docs
   - ReDoc: http://localhost:8000/redoc

3. **Test the API**
   ```bash
   curl -X POST "http://localhost:8000/intent/extract" \
     -H "Content-Type: application/json" \
     -d '{"prompt": "Compare security features of top CRM platforms"}'
   ```

## 📖 API Usage

### Extract Intent from Natural Language

```python
import requests

response = requests.post(
    "http://localhost:8000/intent/extract",
    json={"prompt": "Compare security features of Electronic Lab Notebook vendors in EU and US markets"}
)

intent = response.json()
print(f"Industry: {intent['intent']['market']['industry_name']}")
print(f"Primary Goal: {intent['intent']['scope']['primary_goal']}")
```

### Execute Full Analysis

```python
response = requests.post(
    "http://localhost:8000/analyze/",
    json={
        "prompt": "Compare security features of ELN vendors",
        "options": {
            "depth": "deep_dive",
            "include_screenshots": True
        }
    }
)

analysis = response.json()
analysis_id = analysis["analysis_id"]

# Get HTML report
report = requests.get(f"http://localhost:8000/reports/{analysis_id}?format=html")
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `OPENROUTER_API_KEY` | OpenRouter API key (required) | - |
| `OPENROUTER_MODEL` | OpenRouter model to use | `openai/gpt-4o-mini` |
| `OPENROUTER_BASE_URL` | OpenRouter API base URL | `https://openrouter.ai/api/v1` |
| `DATABASE_URL` | Database connection string | `sqlite:///./ica_app.db` |
| `REDIS_URL` | Redis connection string | - |
| `LOG_LEVEL` | Logging level | `INFO` |

### Rate Limits

| Endpoint | Limit | Window |
|----------|-------|--------|
| `/intent/extract` | 30 requests | 1 minute |
| `/analyze` | 5 requests | 1 minute |
| `/screenshots/capture` | 20 requests | 1 minute |

## 🧪 Testing

Run the test suite:

```bash
# Install test dependencies
pip install pytest pytest-asyncio pytest-cov

# Run tests
pytest tests/ -v

# Run with coverage
pytest tests/ --cov=src --cov-report=html
```

## 📊 Monitoring

The application includes comprehensive monitoring:

- **Health Checks**: `/health` endpoint for system status
- **Structured Logging**: JSON logs with correlation IDs
- **OpenTelemetry**: Distributed tracing support
- **Metrics**: Request/response metrics and performance data

## 🔒 Security

- **Input Validation**: Pydantic schema validation
- **Prompt Injection Protection**: OWASP LLM Top-10 compliance
- **Rate Limiting**: Per-endpoint rate limits
- **GDPR Compliance**: PII detection and data minimization
- **Security Headers**: Comprehensive security headers

## 📚 Documentation

- **API Specification**: Complete OpenAPI 3.0 spec at `/openapi.json`
- **System Design**: See `docs/spec/system-design.md`
- **Data Contracts**: See `docs/spec/data-contracts.md`
- **Compliance**: See `docs/spec/compliance-safety.md`

## 🛣️ Roadmap

### Current Status (M0-M1)
- ✅ Core API structure and intent extraction
- ✅ Comprehensive data models and validation
- ✅ Security and compliance framework
- 🚧 Research pipeline implementation
- 🚧 Screenshot capture with Playwright
- 🚧 Analysis and reporting features

### Upcoming Features
- Strategic framework analysis (Porter, PESTEL, JTBD)
- Advanced AI-powered insights
- Interactive HTML reports
- Multi-format export (PDF, CSV)
- Real-time competitive monitoring

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the `/docs` directory for detailed specifications
- **Issues**: Report bugs and request features via GitHub Issues
- **API Help**: Use the interactive documentation at `/docs`

## 🙏 Acknowledgments

- OpenAI for Structured Outputs and GPT models
- Playwright team for reliable web automation
- FastAPI community for the excellent framework
- Pydantic for robust data validation