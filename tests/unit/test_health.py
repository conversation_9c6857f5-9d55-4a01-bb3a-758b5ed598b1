"""
Unit tests for health check endpoints.
"""

import pytest
from fastapi.testclient import TestClient

def test_health_check(client: TestClient):
    """Test basic health check endpoint."""
    response = client.get("/health/")
    
    assert response.status_code == 200
    data = response.json()
    
    assert data["status"] == "healthy"
    assert "timestamp" in data
    assert "version" in data
    assert "dependencies" in data
    assert "metrics" in data

def test_readiness_check(client: TestClient):
    """Test readiness probe endpoint."""
    response = client.get("/health/ready")
    
    assert response.status_code == 200
    data = response.json()
    
    assert data["status"] == "ready"

def test_liveness_check(client: TestClient):
    """Test liveness probe endpoint."""
    response = client.get("/health/live")
    
    assert response.status_code == 200
    data = response.json()
    
    assert data["status"] == "alive"