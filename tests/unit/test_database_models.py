"""
Unit tests for database models.
"""

import pytest
from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from src.storage.database import Base
from src.storage.models import AnalysisRecord, DocumentRecord, FindingRecord
from src.storage.service import AnalysisService, DocumentService
from src.models import AnalysisIntent, PrimaryGoal

class TestDatabaseModels:
    """Test database models and relationships."""
    
    @pytest.fixture
    def db_session(self):
        """Create test database session."""
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        Session = sessionmaker(bind=engine)
        session = Session()
        yield session
        session.close()
    
    @pytest.fixture
    def sample_intent(self):
        """Sample AnalysisIntent for testing."""
        return AnalysisIntent(
            scope={"primary_goal": PrimaryGoal.competitive_benchmark_feature},
            market={"industry_name": "Test Industry"},
            company_context={"company_name": "Test Company"},
            outputs={"format": "markdown", "depth": "deep_dive"}
        )
    
    def test_analysis_record_creation(self, db_session, sample_intent):
        """Test creating an analysis record."""
        service = AnalysisService(db_session)
        
        analysis = service.create_analysis(sample_intent, user_id="test-user")
        
        assert analysis.id is not None
        assert analysis.status == AnalysisStatus.pending
        assert analysis.intent_json["market"]["industry_name"] == "Test Industry"
        assert analysis.user_id == "test-user"
        assert analysis.created_at is not None
    
    def test_document_relationship(self, db_session, sample_intent):
        """Test relationship between analysis and documents."""
        analysis_service = AnalysisService(db_session)
        document_service = DocumentService(db_session)
        
        # Create analysis
        analysis = analysis_service.create_analysis(sample_intent)
        
        # Create document
        document = document_service.create_document(
            analysis_id=analysis.id,
            url="https://example.com",
            source_type="web",
            title="Test Document",
            text_content="Test content"
        )
        
        # Test relationship
        db_session.refresh(analysis)
        assert len(analysis.documents) == 1
        assert analysis.documents[0].title == "Test Document"
        assert document.analysis_id == analysis.id
    
    def test_content_deduplication(self, db_session, sample_intent):
        """Test content hash deduplication."""
        analysis_service = AnalysisService(db_session)
        document_service = DocumentService(db_session)
        
        analysis = analysis_service.create_analysis(sample_intent)
        
        # Create first document
        doc1 = document_service.create_document(
            analysis_id=analysis.id,
            url="https://example.com/page1",
            source_type="web",
            text_content="Same content"
        )
        
        # Try to create duplicate content
        duplicate = document_service.find_duplicate_document(doc1.content_hash)
        assert duplicate is not None
        assert duplicate.id == doc1.id
    
    def test_analysis_status_update(self, db_session, sample_intent):
        """Test updating analysis status."""
        service = AnalysisService(db_session)
        
        analysis = service.create_analysis(sample_intent)
        original_created_at = analysis.created_at
        
        # Update to completed
        success = service.update_analysis_status(analysis.id, AnalysisStatus.completed)
        
        assert success is True
        
        # Refresh and check
        db_session.refresh(analysis)
        assert analysis.status == AnalysisStatus.completed
        assert analysis.completed_at is not None
        assert analysis.processing_time_seconds is not None
        assert analysis.processing_time_seconds >= 0
    
    def test_analysis_results_update(self, db_session, sample_intent):
        """Test updating analysis results."""
        service = AnalysisService(db_session)
        
        analysis = service.create_analysis(sample_intent)
        
        results = {
            "feature_matrix": [{"competitor": "Test", "feature": "SSO", "support": "yes"}],
            "summary": "Test analysis complete"
        }
        
        success = service.update_analysis_results(analysis.id, results)
        assert success is True
        
        # Refresh and check
        db_session.refresh(analysis)
        assert analysis.results_json["summary"] == "Test analysis complete"
        assert len(analysis.results_json["feature_matrix"]) == 1