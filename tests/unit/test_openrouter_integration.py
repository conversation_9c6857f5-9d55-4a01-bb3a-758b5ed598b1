"""
Unit tests for OpenRouter integration.
"""

import os
import pytest
from unittest.mock import Mock, patch

from src.llm.requirements import IntentExtractor

class TestOpenRouterIntegration:
    """Test OpenRouter API integration."""
    
    def test_intent_extractor_initialization(self):
        """Test IntentExtractor initializes with OpenRouter configuration."""
        with patch.dict(os.environ, {
            'OPENROUTER_API_KEY': 'test-key',
            'OPENROUTER_MODEL': 'openai/gpt-4o-mini',
            'OPENROUTER_BASE_URL': 'https://openrouter.ai/api/v1'
        }):
            extractor = IntentExtractor()
            
            assert extractor.model == 'openai/gpt-4o-mini'
            assert extractor.client.base_url == 'https://openrouter.ai/api/v1'
    
    def test_custom_headers_set(self):
        """Test that OpenRouter-specific headers are set."""
        with patch.dict(os.environ, {
            'OPENROUTER_API_KEY': 'test-key',
            'OPENROUTER_SITE_URL': 'https://test-site.com',
            'OPENROUTER_APP_NAME': 'Test-App'
        }):
            extractor = IntentExtractor()
            
            # Check that headers are set (this is a bit tricky to test directly)
            # We'll verify the client was initialized with the right parameters
            assert extractor.client.base_url == 'https://openrouter.ai/api/v1'
    
    def test_fallback_to_defaults(self):
        """Test fallback to default values when env vars not set."""
        with patch.dict(os.environ, {'OPENROUTER_API_KEY': 'test-key'}, clear=True):
            extractor = IntentExtractor()
            
            assert extractor.model == 'openai/gpt-4o-mini'  # Default model
            assert extractor.client.base_url == 'https://openrouter.ai/api/v1'  # Default URL
    
    @patch('src.llm.requirements.OpenAI')
    def test_openrouter_api_call_format(self, mock_openai):
        """Test that API calls are formatted correctly for OpenRouter."""
        mock_client = Mock()
        mock_openai.return_value = mock_client
        
        # Mock successful response
        mock_response = Mock()
        mock_response.choices[0].message.content = '{"test": "response"}'
        mock_client.chat.completions.create.return_value = mock_response
        
        with patch.dict(os.environ, {'OPENROUTER_API_KEY': 'test-key'}):
            extractor = IntentExtractor()
            
            # Verify OpenAI client was initialized with OpenRouter config
            mock_openai.assert_called_once()
            call_args = mock_openai.call_args
            
            assert call_args[1]['api_key'] == 'test-key'
            assert call_args[1]['base_url'] == 'https://openrouter.ai/api/v1'
            assert 'default_headers' in call_args[1]