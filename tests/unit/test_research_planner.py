"""
Unit tests for the research planner module.
"""

import pytest
from datetime import datetime

from src.models import AnalysisIntent, Scope, Market, CompanyContext, Competition, Capabilities, Outputs
from src.models.enums import PrimaryGoal, OutputFormat, OutputDepth, CompetitorType
from src.planner import ResearchPlanner, SourceType, SourcePriority, QueryType

class TestResearchPlanner:
    """Test cases for ResearchPlanner."""
    
    @pytest.fixture
    def sample_intent(self):
        """Create a sample analysis intent for testing."""
        return AnalysisIntent(
            scope=Scope(
                primary_goal=PrimaryGoal.competitive_benchmark_feature,
                secondary_questions=["Which security features are table stakes?"],
                desired_outputs=["feature_matrix", "screenshot_pack"]
            ),
            market=Market(
                industry_name="Electronic Lab Notebook (ELN)",
                subsector="Bioprocess analytics",
                geographies=["EU", "US"]
            ),
            company_context=CompanyContext(
                company_name="Laboperator",
                product_names=["Workflow Orchestrator"]
            ),
            competition=Competition(
                competitor_list_explicit=["Benchling", "IDBS", "RevLab"],
                competitor_types=[CompetitorType.direct, CompetitorType.indirect],
                evidence_sources=["vendor_sites", "pricing_pages", "docs", "g2"]
            ),
            capabilities=Capabilities(
                feature_taxonomy=["device_control", "audit_trails", "permissions", "integrations", "api"]
            ),
            outputs=Outputs(
                format=OutputFormat.markdown,
                depth=OutputDepth.deep_dive,
                confidence_threshold=0.8
            )
        )
    
    @pytest.fixture
    def planner(self):
        """Create a research planner instance."""
        return ResearchPlanner()
    
    def test_create_research_plan_basic(self, planner, sample_intent):
        """Test basic research plan creation."""
        plan = planner.create_research_plan(sample_intent, "test_analysis_123")
        
        # Verify basic plan structure
        assert plan.analysis_id == "test_analysis_123"
        assert plan.intent_summary is not None
        assert len(plan.intent_summary) > 0
        
        # Verify competitors
        assert len(plan.competitors) == 3
        competitor_names = [c.name for c in plan.competitors]
        assert "Benchling" in competitor_names
        assert "IDBS" in competitor_names
        assert "RevLab" in competitor_names
        
        # Verify phases
        assert len(plan.phases) > 0
        phase_names = [p.name for p in plan.phases]
        assert "Source Discovery" in phase_names
        assert "Content Collection" in phase_names
        
        # Verify queries
        assert plan.total_queries > 0
        assert len(plan.priority_queries) > 0
        
        # Verify effort estimates
        assert plan.estimated_total_duration_minutes > 0
        assert plan.estimated_sources_to_collect > 0
        assert plan.estimated_api_calls > 0
    
    def test_competitor_profiles_generation(self, planner, sample_intent):
        """Test competitor profile generation."""
        plan = planner.create_research_plan(sample_intent)
        
        # Check competitor priorities
        competitors = sorted(plan.competitors, key=lambda c: c.priority)
        assert competitors[0].name == "Benchling"  # First mentioned = highest priority
        assert competitors[0].priority == 1
        
        # Check expected sources
        for competitor in plan.competitors:
            assert len(competitor.expected_sources) > 0
            # Should have critical sources for feature analysis
            source_types = list(competitor.expected_sources.keys())
            assert SourceType.PRODUCT_PAGES in source_types or SourceType.DOCUMENTATION in source_types
    
    def test_source_prioritization_by_goal(self, planner):
        """Test that source priorities change based on analysis goal."""
        # Test pricing analysis intent
        pricing_intent = AnalysisIntent(
            scope=Scope(primary_goal=PrimaryGoal.pricing_analysis),
            market=Market(industry_name="Test Industry"),
            company_context=CompanyContext(company_name="Test Company"),
            competition=Competition(competitor_list_explicit=["Competitor A"]),
            outputs=Outputs()
        )
        
        pricing_plan = planner.create_research_plan(pricing_intent)
        
        # Should prioritize pricing-related sources
        pricing_targets = []
        for phase in pricing_plan.phases:
            for target in phase.targets:
                if target.source_type == SourceType.PRICING_PAGE:
                    pricing_targets.append(target)
        
        assert len(pricing_targets) > 0
        assert any(target.priority == SourcePriority.CRITICAL for target in pricing_targets)
    
    def test_competitor_discovery_needed(self, planner):
        """Test competitor discovery logic."""
        # Intent with no competitors should need discovery
        no_competitors_intent = AnalysisIntent(
            scope=Scope(primary_goal=PrimaryGoal.market_entry_assessment),
            market=Market(industry_name="Test Industry"),
            company_context=CompanyContext(company_name="Test Company"),
            outputs=Outputs()
        )
        
        plan = planner.create_research_plan(no_competitors_intent)
        assert plan.competitor_discovery_needed is True
        
        # Should have competitor discovery phase
        phase_names = [p.name for p in plan.phases]
        assert "Competitor Discovery" in phase_names
    
    def test_query_generation(self, planner, sample_intent):
        """Test search query generation."""
        plan = planner.create_research_plan(sample_intent)
        
        # Should have various query types
        query_types = set()
        for query in plan.priority_queries:
            query_types.add(query.query_type)
        
        assert QueryType.SOURCE_DISCOVERY in query_types
        assert QueryType.FEATURE_RESEARCH in query_types
        
        # Queries should include competitor names
        query_texts = [q.query.lower() for q in plan.priority_queries]
        assert any("benchling" in text for text in query_texts)
        assert any("idbs" in text for text in query_texts)
    
    def test_success_criteria_definition(self, planner, sample_intent):
        """Test success criteria definition."""
        plan = planner.create_research_plan(sample_intent)
        
        # Should have general success criteria
        assert len(plan.success_criteria) > 0
        criteria_text = " ".join(plan.success_criteria).lower()
        assert "confidence" in criteria_text
        assert "evidence" in criteria_text
        
        # Should have goal-specific criteria for feature analysis
        assert any("feature" in criterion.lower() for criterion in plan.success_criteria)
    
    def test_quality_thresholds(self, planner, sample_intent):
        """Test quality threshold definition."""
        plan = planner.create_research_plan(sample_intent)
        
        # Should have quality thresholds
        assert len(plan.quality_thresholds) > 0
        assert "minimum_confidence" in plan.quality_thresholds
        assert plan.quality_thresholds["minimum_confidence"] == 0.8  # From intent
        assert "citation_coverage" in plan.quality_thresholds
        assert plan.quality_thresholds["citation_coverage"] == 1.0  # Should be 100%
    
    def test_intent_summary_generation(self, planner, sample_intent):
        """Test intent summary generation."""
        plan = planner.create_research_plan(sample_intent)
        
        summary = plan.intent_summary
        assert "Competitive Benchmark Feature" in summary
        assert "Electronic Lab Notebook (ELN)" in summary
        assert "Benchling" in summary
        assert "device_control" in summary or "audit_trails" in summary
    
    def test_effort_estimation(self, planner, sample_intent):
        """Test effort estimation calculations."""
        plan = planner.create_research_plan(sample_intent)
        
        # Estimates should be reasonable
        assert 10 <= plan.estimated_total_duration_minutes <= 300  # 10 minutes to 5 hours
        assert plan.estimated_sources_to_collect > 0
        assert plan.estimated_api_calls >= plan.total_queries
        
        # Duration should be sum of phase durations
        phase_duration_sum = sum(p.estimated_duration_minutes for p in plan.phases)
        assert plan.estimated_total_duration_minutes == phase_duration_sum