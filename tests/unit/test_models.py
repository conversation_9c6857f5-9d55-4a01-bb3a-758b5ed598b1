"""
Unit tests for Pydantic models.
"""

import pytest
from pydantic import ValidationError

from src.models import AnalysisIntent, Money, PrimaryGoal

class TestMoney:
    """Test Money model validation."""
    
    def test_valid_money_creation(self):
        """Test creation of valid Money object."""
        money = Money(amount=100.50, currency="USD")
        assert money.amount == 100.50
        assert money.currency == "USD"
    
    def test_negative_amount_validation(self):
        """Test validation of negative amounts."""
        with pytest.raises(ValidationError):
            Money(amount=-10.0, currency="USD")
    
    def test_invalid_currency_code(self):
        """Test validation of currency code length."""
        with pytest.raises(ValidationError):
            Money(amount=100.0, currency="INVALID")
    
    def test_currency_conversion(self):
        """Test currency conversion functionality."""
        usd_money = Money(amount=100.0, currency="USD")
        converted = usd_money.to_usd(exchange_rate=0.85)
        assert converted.amount == 85.0
        assert converted.currency == "USD"

class TestAnalysisIntent:
    """Test AnalysisIntent model validation."""
    
    def test_valid_analysis_intent(self, sample_analysis_intent):
        """Test creation of valid AnalysisIntent."""
        intent = AnalysisIntent(**sample_analysis_intent)
        assert intent.market.industry_name == "Test Industry"
        assert intent.scope.primary_goal == PrimaryGoal.competitive_benchmark_feature
    
    def test_invalid_primary_goal(self, sample_analysis_intent):
        """Test validation of invalid primary goal."""
        sample_analysis_intent["scope"]["primary_goal"] = "invalid_goal"
        
        with pytest.raises(ValidationError) as exc_info:
            AnalysisIntent(**sample_analysis_intent)
        
        assert "primary_goal" in str(exc_info.value)
    
    def test_missing_required_fields(self):
        """Test validation of missing required fields."""
        with pytest.raises(ValidationError):
            AnalysisIntent(scope={}, market={}, company_context={}, outputs={})