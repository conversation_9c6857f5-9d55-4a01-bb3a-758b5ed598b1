# Industry & Competitor Analysis App - Data Contracts

## Overview

This document defines all data models, schemas, and contracts used throughout the application. All models use Pydantic v2 for validation and automatic JSON Schema generation.

## Core Enums

### Analysis Enums

```python
class PrimaryGoal(str, Enum):
    market_entry_assessment = "market_entry_assessment"
    competitive_benchmark_feature = "competitive_benchmark_feature"
    competitive_benchmark_product = "competitive_benchmark_product"
    company_vs_competitors = "company_vs_competitors"
    feature_gap_recommendations = "feature_gap_recommendations"
    pricing_review = "pricing_review"
    positioning_messaging_review = "positioning_messaging_review"
    gtm_strategy_review = "gtm_strategy_review"
    market_size_forecast = "market_size_forecast"
    risk_scan_regulatory = "risk_scan_regulatory"

class OutputDepth(str, Enum):
    quick_scan = "quick_scan"
    deep_dive = "deep_dive"

class OutputFormat(str, Enum):
    markdown = "markdown"
    html = "html"
    pdf = "pdf"
    csv = "csv"
    json = "json"
```

### Business Enums

```python
class ProductType(str, Enum):
    saas = "saas"
    on_prem = "on_prem"
    hardware_plus_software = "hardware_plus_software"
    services = "services"

class MarketPosition(str, Enum):
    leader = "leader"
    challenger = "challenger"
    niche = "niche"
    new_entrant = "new_entrant"
    unknown = "unknown"

class CompetitorType(str, Enum):
    direct = "direct"
    indirect = "indirect"
    substitute = "substitute"
    status_quo = "status_quo"

class SupportLevel(str, Enum):
    yes = "yes"
    partial = "partial"
    no = "no"
    na = "n/a"
    unknown = "unknown"
```

### Pricing Enums

```python
class PricingModel(str, Enum):
    seat = "seat"
    usage = "usage"
    tiered = "tiered"
    flat = "flat"
    freemium = "freemium"
    open_source_dual = "open_source_dual"
    payg = "payg"
    perpetual_license = "perpetual_license"

class BillingCycle(str, Enum):
    monthly = "monthly"
    annual = "annual"
    one_time = "one_time"

class Unit(str, Enum):
    user = "user"
    seat = "seat"
    device = "device"
    event = "event"
    gb = "gb"
    workspace = "workspace"
    org = "org"
    unlimited = "unlimited"
    other = "other"
```

## Core Data Models

### 1. AnalysisIntent (Primary Input Schema)

```python
class AnalysisIntent(BaseModel):
    """
    Complete structured representation of analysis requirements.
    Generated from natural language prompts via OpenAI Structured Outputs.
    """
    scope: Scope
    market: Market
    company_context: CompanyContext
    competition: Optional[Competition] = None
    capabilities: Optional[Capabilities] = None
    pricing: Optional[Pricing] = None
    gtm: Optional[GTM] = None
    evidence_signals: Optional[EvidenceSignals] = None
    constraints_ethics: Optional[ConstraintsEthics] = None
    automation: Optional[Automation] = None
    advanced: Optional[Advanced] = None
    outputs: Outputs

    model_config = ConfigDict(
        json_schema_extra={
            "examples": [
                {
                    "scope": {
                        "primary_goal": "competitive_benchmark_feature",
                        "secondary_questions": [
                            "Which security features are table stakes?",
                            "What do leaders gate behind higher tiers?"
                        ]
                    },
                    "market": {
                        "industry_name": "Electronic Lab Notebook (ELN)",
                        "geographies": ["EU", "US"]
                    },
                    "company_context": {
                        "company_name": "Laboperator",
                        "product_type": "saas"
                    },
                    "outputs": {
                        "format": "markdown",
                        "depth": "deep_dive"
                    }
                }
            ]
        }
    )
```

### 2. EvidenceDocument

```python
class EvidenceDocument(BaseModel):
    """
    Represents a single piece of evidence collected during research.
    """
    url: str = Field(description="Source URL")
    source_type: SourceType = Field(description="Type of source")
    title: str = Field(description="Document title")
    published_at: Optional[datetime] = Field(default=None, description="Publication date")
    scraped_at: datetime = Field(description="When content was scraped")
    text: str = Field(description="Extracted text content")
    lang: str = Field(default="en", description="Language code")
    entities: List[Entity] = Field(default_factory=list, description="Extracted entities")
    hash: str = Field(description="Content hash for deduplication")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")

class SourceType(str, Enum):
    web = "web"
    pdf = "pdf"
    filing = "filing"
    pricing = "pricing"
    review = "review"
    documentation = "documentation"
    changelog = "changelog"
    press_release = "press_release"

class Entity(BaseModel):
    type: EntityType
    text: str
    confidence: float = Field(ge=0.0, le=1.0)
    start_offset: Optional[int] = None
    end_offset: Optional[int] = None

class EntityType(str, Enum):
    company = "company"
    product = "product"
    feature = "feature"
    metric = "metric"
    price = "price"
    person = "person"
    location = "location"
```

### 3. Finding/Claim

```python
class Finding(BaseModel):
    """
    Represents a specific claim or insight extracted from evidence.
    """
    claim_id: str = Field(description="Unique identifier")
    topic: ClaimTopic = Field(description="What this claim is about")
    target: str = Field(description="Company, product, or feature being claimed about")
    value: Union[str, float, bool] = Field(description="The claim value")
    confidence: float = Field(ge=0.0, le=1.0, description="Confidence score")
    evidence: List[Evidence] = Field(description="Supporting evidence")
    verified: Optional[bool] = Field(default=None, description="Verification status")
    created_at: datetime = Field(default_factory=datetime.utcnow)

class ClaimTopic(str, Enum):
    feature = "feature"
    pricing = "pricing"
    positioning = "positioning"
    metric = "metric"
    capability = "capability"
    limitation = "limitation"

class Evidence(BaseModel):
    url: str
    quote: str = Field(description="Exact quote supporting the claim")
    start_offset: Optional[int] = None
    end_offset: Optional[int] = None
    screenshot_path: Optional[str] = None
    confidence: float = Field(ge=0.0, le=1.0)
```

### 4. FeatureMatrixRow

```python
class FeatureMatrixRow(BaseModel):
    """
    Single row in competitive feature matrix.
    """
    competitor: str = Field(description="Competitor name")
    feature: str = Field(description="Feature name from taxonomy")
    support_level: SupportLevel = Field(description="Level of support")
    confidence: float = Field(ge=0.0, le=1.0, description="Confidence in assessment")
    evidence_url: str = Field(description="Primary evidence URL")
    evidence_quote: Optional[str] = Field(description="Supporting quote")
    screenshot_path: Optional[str] = Field(description="Screenshot evidence")
    notes: Optional[str] = Field(description="Additional context")
    last_verified: datetime = Field(default_factory=datetime.utcnow)

class FeatureMatrix(BaseModel):
    """
    Complete feature comparison matrix.
    """
    features: List[str] = Field(description="List of features being compared")
    competitors: List[str] = Field(description="List of competitors")
    matrix: List[FeatureMatrixRow] = Field(description="Matrix data")
    metadata: FeatureMatrixMetadata

class FeatureMatrixMetadata(BaseModel):
    created_at: datetime
    feature_taxonomy_version: str
    confidence_threshold: float
    total_features: int
    total_competitors: int
    coverage_percentage: float
```

### 5. PricingTierNormalized

```python
class PricingTierNormalized(BaseModel):
    """
    Normalized pricing information for comparison.
    """
    competitor: str
    tier_name: str
    price: Money
    billing_cycle: BillingCycle
    unit: Unit
    features_included: List[str] = Field(default_factory=list)
    limitations: List[str] = Field(default_factory=list)
    overages: Optional[Money] = Field(description="Overage pricing")
    discounts: Optional[str] = Field(description="Available discounts")
    free_trial: Optional[str] = Field(description="Free trial details")
    evidence_url: str
    last_updated: datetime = Field(default_factory=datetime.utcnow)

class Money(BaseModel):
    amount: float = Field(ge=0, description="Monetary amount")
    currency: str = Field(min_length=3, max_length=3, description="ISO currency code")
    
    def to_usd(self, exchange_rate: float = 1.0) -> 'Money':
        """Convert to USD using provided exchange rate."""
        return Money(amount=self.amount * exchange_rate, currency="USD")

class PricingComparison(BaseModel):
    """
    Comparative pricing analysis across competitors.
    """
    competitors: List[str]
    tiers: List[PricingTierNormalized]
    analysis: PricingAnalysis

class PricingAnalysis(BaseModel):
    lowest_entry_price: Money
    highest_enterprise_price: Money
    common_billing_cycles: List[BillingCycle]
    pricing_model_distribution: Dict[PricingModel, int]
    feature_gating_patterns: List[str]
```

### 6. Strategic Frameworks

```python
class PorterFiveForcesAnalysis(BaseModel):
    """
    Porter's Five Forces analysis results.
    """
    rivalry: PorterForceAnalysis
    threat_of_new_entrants: PorterForceAnalysis
    bargaining_power_buyers: PorterForceAnalysis
    bargaining_power_suppliers: PorterForceAnalysis
    threat_of_substitutes: PorterForceAnalysis
    overall_assessment: str
    created_at: datetime = Field(default_factory=datetime.utcnow)

class PorterForceAnalysis(BaseModel):
    strength: ForceStrength
    description: str
    key_factors: List[str]
    evidence: List[Evidence]

class ForceStrength(str, Enum):
    very_low = "very_low"
    low = "low"
    medium = "medium"
    high = "high"
    very_high = "very_high"

class PESTELAnalysis(BaseModel):
    """
    PESTEL analysis results.
    """
    political: List[str]
    economic: List[str]
    social: List[str]
    technological: List[str]
    environmental: List[str]
    legal: List[str]
    key_insights: List[str]
    created_at: datetime = Field(default_factory=datetime.utcnow)

class JTBDAnalysis(BaseModel):
    """
    Jobs-to-be-Done analysis results.
    """
    jobs: List[JTBDJob]
    key_insights: List[str]
    opportunity_areas: List[str]
    created_at: datetime = Field(default_factory=datetime.utcnow)

class JTBDJob(BaseModel):
    job: str
    pains: List[str]
    desired_outcomes: List[str]
    current_solutions: List[str]
    opportunity_score: Optional[float] = Field(ge=0.0, le=10.0)
```

### 7. ReportSummary

```python
class ReportSummary(BaseModel):
    """
    Complete analysis report structure.
    """
    analysis_id: str
    created_at: datetime
    completed_at: Optional[datetime]
    status: AnalysisStatus
    intent: AnalysisIntent
    executive_summary: str
    key_findings: List[str]
    feature_matrix: Optional[FeatureMatrix]
    pricing_analysis: Optional[PricingComparison]
    porter_analysis: Optional[PorterFiveForcesAnalysis]
    pestel_analysis: Optional[PESTELAnalysis]
    jtbd_analysis: Optional[JTBDAnalysis]
    sources: List[SourceReference]
    screenshots: List[ScreenshotReference]
    confidence_metrics: ConfidenceMetrics
    processing_metrics: ProcessingMetrics

class AnalysisStatus(str, Enum):
    pending = "pending"
    processing = "processing"
    completed = "completed"
    failed = "failed"
    cancelled = "cancelled"

class SourceReference(BaseModel):
    url: str
    title: str
    source_type: SourceType
    accessed_at: datetime
    relevance_score: float = Field(ge=0.0, le=1.0)

class ScreenshotReference(BaseModel):
    path: str
    url: str
    label: str
    timestamp: datetime
    viewport: Viewport
    selectors: List[str] = Field(default_factory=list)

class ConfidenceMetrics(BaseModel):
    overall_confidence: float = Field(ge=0.0, le=1.0)
    feature_matrix_confidence: Optional[float] = Field(ge=0.0, le=1.0)
    pricing_confidence: Optional[float] = Field(ge=0.0, le=1.0)
    source_diversity_score: float = Field(ge=0.0, le=1.0)
    evidence_quality_score: float = Field(ge=0.0, le=1.0)

class ProcessingMetrics(BaseModel):
    total_processing_time_seconds: float
    sources_discovered: int
    sources_successfully_processed: int
    screenshots_captured: int
    llm_api_calls: int
    total_tokens_used: int
    cache_hit_rate: float = Field(ge=0.0, le=1.0)
```

## JSON Schema Generation

All Pydantic models automatically generate JSON Schema for OpenAI Structured Outputs:

```python
# Generate JSON Schema for AnalysisIntent
intent_schema = AnalysisIntent.model_json_schema()

# Use with OpenAI Structured Outputs
response_format = {
    "type": "json_schema",
    "json_schema": {
        "name": "analysis_intent_schema",
        "schema": intent_schema,
        "strict": True
    }
}
```

## Validation Rules

### Field Validation

```python
class AnalysisIntent(BaseModel):
    # String length constraints
    industry_name: str = Field(min_length=2, max_length=100)
    
    # Numeric constraints
    confidence_threshold: float = Field(ge=0.0, le=1.0)
    
    # List constraints
    competitors: List[str] = Field(min_length=1, max_length=20)
    
    # Custom validation
    @field_validator('geographies')
    @classmethod
    def validate_geographies(cls, v):
        valid_regions = {'US', 'EU', 'APAC', 'LATAM', 'MEA', 'Global'}
        for geo in v:
            if geo not in valid_regions:
                raise ValueError(f'Invalid geography: {geo}')
        return v
```

### Model Validation

```python
class PricingTier(BaseModel):
    price: Money
    billing_cycle: BillingCycle
    
    @model_validator(mode='after')
    def validate_pricing_consistency(self):
        # Custom business logic validation
        if self.billing_cycle == BillingCycle.one_time and self.price.amount < 100:
            raise ValueError('One-time pricing must be at least $100')
        return self
```

## Database Schema

### SQLAlchemy Models

```python
class AnalysisRecord(Base):
    __tablename__ = 'analyses'
    
    id = Column(String, primary_key=True)
    status = Column(Enum(AnalysisStatus))
    intent_json = Column(JSON)  # Stores AnalysisIntent
    results_json = Column(JSON)  # Stores ReportSummary
    created_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)
    
    # Relationships
    documents = relationship("DocumentRecord", back_populates="analysis")
    findings = relationship("FindingRecord", back_populates="analysis")

class DocumentRecord(Base):
    __tablename__ = 'documents'
    
    id = Column(String, primary_key=True)
    analysis_id = Column(String, ForeignKey('analyses.id'))
    url = Column(String, nullable=False)
    source_type = Column(Enum(SourceType))
    title = Column(String)
    content_hash = Column(String, unique=True)
    scraped_at = Column(DateTime, default=datetime.utcnow)
    
    # Full-text search
    text_content = Column(Text)
    search_vector = Column(TSVectorType('text_content'))
```

## API Response Formats

### Success Response Wrapper

```python
class APIResponse(BaseModel, Generic[T]):
    """
    Standard API response wrapper.
    """
    success: bool = True
    data: T
    metadata: Optional[Dict[str, Any]] = None
    request_id: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)

# Usage
class AnalysisResponse(APIResponse[ReportSummary]):
    pass
```

### Error Response

```python
class APIError(BaseModel):
    """
    Standard error response format.
    """
    success: bool = False
    error: ErrorDetail
    request_id: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class ErrorDetail(BaseModel):
    code: str
    message: str
    details: Optional[Dict[str, Any]] = None
    field: Optional[str] = None  # For validation errors
```

## Schema Evolution

### Versioning Strategy

```python
class AnalysisIntentV1(BaseModel):
    """Version 1 of AnalysisIntent schema."""
    model_config = ConfigDict(
        json_schema_extra={"version": "1.0"}
    )

class AnalysisIntentV2(AnalysisIntentV1):
    """Version 2 with additional fields."""
    new_field: Optional[str] = None
    model_config = ConfigDict(
        json_schema_extra={"version": "2.0"}
    )
```

### Migration Support

```python
def migrate_intent_v1_to_v2(v1_data: dict) -> dict:
    """Migrate AnalysisIntent from v1 to v2."""
    v2_data = v1_data.copy()
    v2_data['new_field'] = None  # Set default for new field
    return v2_data
```

## Testing Schemas

### Test Data Factories

```python
class AnalysisIntentFactory:
    """Factory for creating test AnalysisIntent objects."""
    
    @staticmethod
    def create_basic() -> AnalysisIntent:
        return AnalysisIntent(
            scope=Scope(primary_goal=PrimaryGoal.competitive_benchmark_feature),
            market=Market(industry_name="Test Industry"),
            company_context=CompanyContext(company_name="Test Company"),
            outputs=Outputs()
        )
    
    @staticmethod
    def create_with_competitors(competitors: List[str]) -> AnalysisIntent:
        intent = AnalysisIntentFactory.create_basic()
        intent.competition = Competition(competitor_list_explicit=competitors)
        return intent
```

This comprehensive data contract specification ensures type safety, validation, and consistency across the entire application while supporting future evolution and testing needs.