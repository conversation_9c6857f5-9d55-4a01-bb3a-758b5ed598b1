# Industry & Competitor Analysis App - System Design

## Architecture Overview

The system follows a modular, pipeline-based architecture that transforms user prompts into comprehensive competitive analysis reports through a series of specialized components.

### High-Level Data Flow

```
User Prompt → Intent Extraction → Research Planning → Evidence Harvesting → 
Content Extraction → Analysis & Scoring → Synthesis → Report Generation
```

## Core Modules

### 1. Intent & Requirements Module (`src/llm/`)
**Purpose**: Convert natural language to structured analysis requirements

**Components**:
- `requirements.py`: OpenAI Structured Outputs integration
- `schemas.py`: Pydantic models and JSON Schema definitions
- `validation.py`: Input validation and sanitization

**Key Technologies**:
- OpenAI Responses API with Structured Outputs (strict mode)
- Pydantic v2 for data validation
- JSON Schema for contract enforcement

### 2. Research Planner Module (`src/planner/`)
**Purpose**: Generate comprehensive research strategy from requirements

**Components**:
- `plan.py`: Research plan generation
- `sources.py`: Source type definitions and URL patterns
- `queries.py`: Search query template generation

**Responsibilities**:
- Competitor discovery and expansion
- Source prioritization (pricing, docs, reviews, filings)
- Query optimization for different search engines

### 3. Web Harvester Module (`src/crawler/`)
**Purpose**: Ethical web crawling and screenshot capture

**Components**:
- `playwright_runner.py`: Browser automation and screenshots
- `robots.py`: Robots.txt compliance checking
- `rate_limiter.py`: Polite crawling with backoff
- `content_fetcher.py`: HTTP content retrieval

**Key Features**:
- Playwright-based screenshot capture with PII masking
- Robots.txt parsing and compliance
- Configurable rate limiting and retry logic
- Viewport and geolocation emulation

### 4. Content Extractor Module (`src/extract/`)
**Purpose**: Clean content extraction from various formats

**Components**:
- `html.py`: Trafilatura-based HTML text extraction
- `pdf.py`: PyMuPDF/pdfplumber PDF processing
- `structured.py`: Table and form data extraction
- `metadata.py`: Document metadata extraction

**Capabilities**:
- Clean, readable text extraction
- Table detection and parsing
- Metadata preservation (title, publish date, language)
- Content deduplication

### 5. Evidence Store Module (`src/storage/`)
**Purpose**: Structured storage and retrieval of collected evidence

**Components**:
- `database.py`: SQLite/PostgreSQL database interface
- `models.py`: Database schema definitions
- `indexing.py`: Full-text search and indexing
- `caching.py`: Content caching and TTL management

**Schema**:
```sql
documents (id, url, source_type, title, published_at, scraped_at, text, lang, hash)
entities (id, doc_id, type, text, confidence)
claims (id, topic, target, value, confidence, evidence_refs)
screenshots (id, doc_id, path, viewport, selectors, timestamp)
```

### 6. Analyzer Module (`src/analyze/`)
**Purpose**: AI-powered analysis and insight generation

**Components**:
- `feature_mapper.py`: Feature taxonomy mapping with confidence scores
- `pricing_parser.py`: Pricing tier extraction and normalization
- `frameworks.py`: Porter/PESTEL/JTBD framework generation
- `comparator.py`: Competitive comparison and gap analysis
- `verifier.py`: Dual-pass claim verification

**Analysis Types**:
- Feature matrix generation with support levels
- Pricing normalization (currency, billing cycles, units)
- Strategic framework summaries with citations
- Competitive positioning analysis

### 7. Reporter Module (`src/report/`)
**Purpose**: Multi-format report generation

**Components**:
- `html.py`: Interactive HTML report generation
- `pdf.py`: PDF export using WeasyPrint
- `csv.py`: Data export for spreadsheet analysis
- `templates.py`: Report template management

**Output Formats**:
- Interactive HTML with embedded screenshots
- PDF reports with proper citations
- CSV exports for feature matrices and pricing
- JSON API responses for programmatic access

### 8. Safety & Compliance Module (`src/safety/`)
**Purpose**: Ethical and legal compliance

**Components**:
- `gdpr.py`: GDPR compliance utilities
- `robots_checker.py`: Robots.txt validation
- `prompt_injection.py`: LLM security scanning
- `content_filter.py`: PII detection and masking

**Compliance Features**:
- GDPR data minimization and retention policies
- OWASP LLM Top-10 security measures
- Automated PII detection and redaction
- Audit logging for compliance reporting

## Data Models

### Core Pydantic Models

```python
class AnalysisIntent(BaseModel):
    scope: Scope
    market: Market
    company_context: CompanyContext
    competition: Optional[Competition]
    capabilities: Optional[Capabilities]
    pricing: Optional[Pricing]
    outputs: Outputs

class EvidenceDocument(BaseModel):
    url: str
    source_type: SourceType
    title: str
    published_at: Optional[datetime]
    scraped_at: datetime
    text: str
    lang: str
    entities: List[Entity]
    hash: str

class Finding(BaseModel):
    claim_id: str
    topic: str  # feature|pricing|positioning|metric
    target: str  # company|product|feature
    value: Union[str, float, bool]
    confidence: float
    evidence: List[Evidence]

class FeatureMatrixRow(BaseModel):
    competitor: str
    feature: str
    support_level: SupportLevel
    confidence: float
    evidence_url: str
    screenshot_path: Optional[str]
```

## Error Handling Strategy

### Graceful Degradation
- **Partial Results**: Return available data even if some sources fail
- **Confidence Scoring**: Lower confidence for incomplete data
- **Fallback Sources**: Alternative data sources when primary fails

### Error Categories
1. **Network Errors**: Retry with exponential backoff
2. **Rate Limiting**: Respect 429 responses with proper delays
3. **Content Extraction Failures**: Log and continue with other sources
4. **LLM API Errors**: Implement circuit breaker pattern
5. **Schema Validation Errors**: Detailed error messages for debugging

### Monitoring & Alerting
- **Health Checks**: Endpoint availability monitoring
- **Error Rate Tracking**: Alert on elevated error rates
- **Performance Metrics**: Response time and throughput monitoring

## Rate Limiting & Throttling

### Web Crawling Limits
- **Default Rate**: 1 request per second per domain
- **Configurable Limits**: Per-domain rate customization
- **Burst Protection**: Token bucket algorithm implementation
- **Robots.txt Compliance**: Respect crawl-delay directives

### API Rate Limiting
- **OpenAI API**: Implement token bucket for API calls
- **Search APIs**: Respect provider-specific limits
- **Internal APIs**: Rate limiting for external consumers

## Retry Logic

### Exponential Backoff
```python
def retry_with_backoff(func, max_retries=3, base_delay=1):
    for attempt in range(max_retries):
        try:
            return func()
        except RetryableError:
            delay = base_delay * (2 ** attempt)
            time.sleep(delay)
    raise MaxRetriesExceeded()
```

### Retry Conditions
- **Network timeouts**: Retry up to 3 times
- **5xx HTTP errors**: Retry with backoff
- **Rate limiting (429)**: Respect Retry-After header
- **Temporary LLM failures**: Retry with different model if available

## Logging & Tracing

### Structured Logging
```python
import structlog

logger = structlog.get_logger()
logger.info("analysis_started", 
           analysis_id=analysis_id,
           industry=intent.market.industry_name,
           competitors=len(competitors))
```

### OpenTelemetry Integration
- **Distributed Tracing**: Track requests across all modules
- **Span Attributes**: Include relevant metadata (tokens, latency, cache hits)
- **Custom Metrics**: Analysis completion time, success rates, error rates

### Log Levels
- **DEBUG**: Detailed execution flow
- **INFO**: Major milestones and results
- **WARN**: Recoverable errors and fallbacks
- **ERROR**: Unrecoverable errors requiring attention

## Secrets Management

### Environment Variables
```bash
OPENAI_API_KEY=sk-...
DATABASE_URL=postgresql://...
REDIS_URL=redis://...
SENTRY_DSN=https://...
```

### Security Best Practices
- **No secrets in code**: All sensitive data in environment variables
- **Rotation Support**: Graceful handling of key rotation
- **Least Privilege**: Minimal required permissions for each component
- **Audit Logging**: Track all secret access and usage

## Scalability Considerations

### Horizontal Scaling
- **Stateless Design**: All components designed for horizontal scaling
- **Database Connection Pooling**: Efficient database resource usage
- **Caching Strategy**: Redis for frequently accessed data
- **Load Balancing**: Support for multiple application instances

### Performance Optimization
- **Concurrent Processing**: Parallel evidence collection and analysis
- **Caching Layers**: Multi-level caching (memory, Redis, CDN)
- **Database Indexing**: Optimized queries for large datasets
- **Content Compression**: Efficient storage and transfer

## Security Architecture

### Input Validation
- **Schema Validation**: Strict Pydantic model validation
- **Prompt Injection Protection**: OWASP LLM Top-10 compliance
- **URL Validation**: Prevent SSRF attacks
- **Content Sanitization**: XSS prevention in generated reports

### Data Protection
- **Encryption at Rest**: Database and file storage encryption
- **Encryption in Transit**: TLS for all external communications
- **PII Detection**: Automated detection and masking
- **Data Retention**: Configurable retention policies

### Access Control
- **API Authentication**: JWT-based authentication
- **Role-Based Access**: Different permission levels
- **Rate Limiting**: Prevent abuse and DoS attacks
- **Audit Logging**: Complete audit trail for compliance