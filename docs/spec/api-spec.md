# Industry & Competitor Analysis App - API Specification

## Base Configuration

- **Base URL**: `http://localhost:8000` (development)
- **API Version**: `v1`
- **Content Type**: `application/json`
- **Authentication**: Bearer token (future implementation)

## Core Endpoints

### 1. Extract Intent

**Endpoint**: `POST /extract-intent`

**Description**: Converts natural language prompts into structured analysis requirements using OpenAI Structured Outputs.

**Request Body**:
```json
{
  "prompt": "Compare security features of top Electronic Lab Notebook vendors in EU and US markets"
}
```

**Response**: `AnalysisIntent` (JSON Schema validated)
```json
{
  "scope": {
    "primary_goal": "competitive_benchmark_feature",
    "secondary_questions": [
      "Which security features are table stakes?",
      "What do leaders gate behind higher tiers?"
    ],
    "desired_outputs": ["feature_matrix", "screenshot_pack"]
  },
  "market": {
    "industry_name": "Electronic Lab Notebook (ELN)",
    "subsector": "Bioprocess analytics",
    "geographies": ["EU", "US"],
    "time_range": {"as_of_date": "2025-08-17"}
  },
  "company_context": {
    "company_name": "Laboperator",
    "product_names": ["Workflow Orchestrator"],
    "product_type": "saas",
    "deployment": "cloud",
    "current_market_position": "challenger"
  },
  "competition": {
    "competitor_list_explicit": ["Benchling", "IDBS", "RevLab"],
    "competitor_types": ["direct", "indirect"],
    "evidence_sources": ["vendor_sites", "pricing_pages", "docs", "g2"]
  },
  "outputs": {
    "format": "markdown",
    "depth": "deep_dive",
    "confidence_threshold": 0.8
  }
}
```

**Error Responses**:
- `400 Bad Request`: Invalid prompt or missing required fields
- `500 Internal Server Error`: OpenAI API failure or schema validation error

### 2. Research

**Endpoint**: `POST /research`

**Description**: Executes research plan based on analysis intent, gathering evidence from web sources.

**Request Body**: `AnalysisIntent` (from `/extract-intent`)

**Response**:
```json
{
  "summary": "Analysis of Electronic Lab Notebook security features reveals...",
  "sources": [
    "https://benchling.com/security",
    "https://idbs.com/compliance",
    "https://revlab.com/features"
  ],
  "evidence_count": 15,
  "processing_time_seconds": 45.2
}
```

**Error Responses**:
- `404 Not Found`: No sources found or extracted
- `429 Too Many Requests`: Rate limiting exceeded
- `500 Internal Server Error`: Research pipeline failure

### 3. Screenshots

**Endpoint**: `POST /screenshots`

**Description**: Captures screenshots of competitor interfaces and key pages.

**Request Body**:
```json
{
  "targets": [
    {
      "url": "https://competitor.com/pricing",
      "label": "Pricing",
      "selectors": ["#pricing-table", ".feature-list"]
    },
    {
      "url": "https://competitor.com/security",
      "label": "Security Features"
    }
  ],
  "out_dir": "screens",
  "viewport": {
    "width": 1440,
    "height": 900
  },
  "wait_until": "networkidle",
  "extra_wait_ms": 800
}
```

**Response**:
```json
{
  "saved": [
    "screens/competitor.com_Pricing_20250817-143022_full.png",
    "screens/competitor.com_a1b2c3d4_20250817-143023.png",
    "screens/competitor.com_Security_Features_20250817-143025_full.png"
  ],
  "total_screenshots": 3,
  "processing_time_seconds": 12.5
}
```

### 4. Analyze (Full Pipeline)

**Endpoint**: `POST /analyze`

**Description**: Orchestrates the complete analysis pipeline from intent to final report.

**Request Body**:
```json
{
  "prompt": "Compare security features of top Electronic Lab Notebook vendors",
  "options": {
    "depth": "deep_dive",
    "include_screenshots": true,
    "output_formats": ["html", "json"],
    "confidence_threshold": 0.8
  }
}
```

**Response**:
```json
{
  "analysis_id": "uuid-12345",
  "status": "completed",
  "created_at": "2025-08-17T14:30:22Z",
  "completed_at": "2025-08-17T14:38:45Z",
  "processing_time_seconds": 503,
  "results": {
    "intent": { /* AnalysisIntent object */ },
    "research_summary": "...",
    "feature_matrix": [
      {
        "competitor": "Benchling",
        "feature": "SSO Integration",
        "support_level": "yes",
        "confidence": 0.95,
        "evidence_url": "https://benchling.com/security",
        "screenshot_path": "screens/benchling_security_20250817.png"
      }
    ],
    "pricing_analysis": [
      {
        "competitor": "Benchling",
        "tier": "Professional",
        "price": {"amount": 89, "currency": "USD"},
        "billing_cycle": "monthly",
        "unit": "user"
      }
    ],
    "frameworks": {
      "porter_forces": {
        "rivalry": "High - established players with strong feature sets",
        "threat_of_new_entrants": "Medium - high switching costs but growing market"
      },
      "pestel": {
        "political": ["GDPR compliance requirements", "Data sovereignty laws"],
        "technological": ["Cloud-first architectures", "AI/ML integration trends"]
      },
      "jtbd": [
        {
          "job": "Track experiments end-to-end",
          "pains": ["Manual data entry", "Version control issues"],
          "desired_outcomes": ["Searchable records", "Audit trails"]
        }
      ]
    },
    "sources": [
      {
        "url": "https://benchling.com/security",
        "title": "Benchling Security & Compliance",
        "accessed_at": "2025-08-17T14:32:15Z",
        "source_type": "vendor_site"
      }
    ]
  }
}
```

### 5. Get Report

**Endpoint**: `GET /reports/{analysis_id}`

**Description**: Retrieves analysis results in various formats.

**Query Parameters**:
- `format`: `json` | `html` | `pdf` | `csv` (default: `json`)
- `section`: `all` | `summary` | `matrix` | `pricing` | `frameworks` (default: `all`)

**Response** (format=html):
```html
<!DOCTYPE html>
<html>
<head>
    <title>Competitive Analysis Report</title>
    <style>/* Embedded CSS */</style>
</head>
<body>
    <h1>Electronic Lab Notebook Security Features Analysis</h1>
    <div class="executive-summary">...</div>
    <div class="feature-matrix">...</div>
    <div class="pricing-analysis">...</div>
    <div class="strategic-frameworks">...</div>
    <div class="sources">...</div>
</body>
</html>
```

### 6. Health Check

**Endpoint**: `GET /health`

**Description**: System health and dependency status.

**Response**:
```json
{
  "status": "healthy",
  "timestamp": "2025-08-17T14:30:22Z",
  "version": "1.0.0",
  "dependencies": {
    "openai_api": "healthy",
    "database": "healthy",
    "playwright": "healthy",
    "redis_cache": "healthy"
  },
  "metrics": {
    "active_analyses": 3,
    "completed_today": 47,
    "average_processing_time_seconds": 425
  }
}
```

## Data Models

### Request/Response Schemas

All API endpoints use Pydantic v2 models with JSON Schema validation:

```python
# Request Models
class ExtractRequest(BaseModel):
    prompt: str = Field(min_length=10, max_length=2000)

class AnalyzeRequest(BaseModel):
    prompt: str
    options: Optional[AnalyzeOptions] = None

class AnalyzeOptions(BaseModel):
    depth: OutputDepth = OutputDepth.deep_dive
    include_screenshots: bool = True
    output_formats: List[OutputFormat] = [OutputFormat.json]
    confidence_threshold: float = Field(default=0.8, ge=0.0, le=1.0)

# Response Models
class ResearchResponse(BaseModel):
    summary: str
    sources: List[str]
    evidence_count: int
    processing_time_seconds: float

class AnalysisResponse(BaseModel):
    analysis_id: str
    status: AnalysisStatus
    created_at: datetime
    completed_at: Optional[datetime]
    processing_time_seconds: Optional[float]
    results: Optional[AnalysisResults]

class FeatureMatrixRow(BaseModel):
    competitor: str
    feature: str
    support_level: SupportLevel
    confidence: float = Field(ge=0.0, le=1.0)
    evidence_url: str
    screenshot_path: Optional[str]

class PricingTierNormalized(BaseModel):
    competitor: str
    tier: str
    price: Money
    billing_cycle: BillingCycle
    unit: Unit
    features_included: List[str]
    limitations: List[str]
```

## Error Handling

### Standard Error Response Format

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Request validation failed",
    "details": {
      "field": "prompt",
      "issue": "Field required"
    },
    "request_id": "req_12345",
    "timestamp": "2025-08-17T14:30:22Z"
  }
}
```

### Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `VALIDATION_ERROR` | 400 | Request validation failed |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests |
| `ANALYSIS_NOT_FOUND` | 404 | Analysis ID not found |
| `PROCESSING_ERROR` | 500 | Internal processing error |
| `EXTERNAL_API_ERROR` | 502 | External service failure |
| `TIMEOUT_ERROR` | 504 | Processing timeout |

## Rate Limiting

### Limits by Endpoint

| Endpoint | Rate Limit | Window |
|----------|------------|--------|
| `/extract-intent` | 60 requests | 1 minute |
| `/research` | 10 requests | 1 minute |
| `/screenshots` | 30 requests | 1 minute |
| `/analyze` | 5 requests | 1 minute |
| `/reports/*` | 100 requests | 1 minute |

### Rate Limit Headers

```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 45
X-RateLimit-Reset: 1692276622
Retry-After: 15
```

## Authentication (Future)

### Bearer Token Authentication

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### API Key Authentication

```http
X-API-Key: ak_live_1234567890abcdef
```

## Webhooks (Future)

### Analysis Completion Webhook

**Endpoint**: User-provided URL
**Method**: `POST`
**Payload**:
```json
{
  "event": "analysis.completed",
  "analysis_id": "uuid-12345",
  "status": "completed",
  "completed_at": "2025-08-17T14:38:45Z",
  "results_url": "https://api.example.com/reports/uuid-12345"
}
```

## OpenAPI Specification

The complete OpenAPI 3.0 specification is available at `/openapi.json` and includes:

- Complete schema definitions for all request/response models
- Detailed parameter descriptions and validation rules
- Example requests and responses for each endpoint
- Error response schemas and codes
- Authentication requirements (when implemented)

## SDK Examples

### Python SDK Usage

```python
from ica_client import ICAClient

client = ICAClient(api_key="your-api-key")

# Extract intent from natural language
intent = client.extract_intent("Compare security features of ELN vendors")

# Run full analysis
analysis = client.analyze(
    prompt="Compare security features of ELN vendors",
    options={
        "depth": "deep_dive",
        "include_screenshots": True
    }
)

# Get results
report = client.get_report(analysis.analysis_id, format="html")
```

### cURL Examples

```bash
# Extract intent
curl -X POST http://localhost:8000/extract-intent \
  -H "Content-Type: application/json" \
  -d '{"prompt": "Compare security features of ELN vendors"}'

# Run full analysis
curl -X POST http://localhost:8000/analyze \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Compare security features of ELN vendors",
    "options": {
      "depth": "deep_dive",
      "include_screenshots": true
    }
  }'

# Get HTML report
curl http://localhost:8000/reports/uuid-12345?format=html
```