# Industry & Competitor Analysis App - Development Makefile

.PHONY: help install install-dev setup test test-unit test-integration test-e2e lint format type-check security-check clean run dev docker-build docker-run docs

# Default target
help: ## Show this help message
	@echo "Industry & Competitor Analysis App - Development Commands"
	@echo "========================================================="
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Environment Setup
install: ## Install production dependencies
	pip install -e .

install-dev: ## Install development dependencies
	pip install -e ".[dev,test,docs]"

setup: ## Complete development environment setup
	@echo "Setting up development environment..."
	python -m venv .venv || true
	@echo "Activate virtual environment with: source .venv/bin/activate"
	@echo "Then run: make install-dev"
	@echo "Copy .env.example to .env and configure your settings"
	@echo "Install Playwright browsers with: playwright install"

# Database
db-init: ## Initialize database
	alembic upgrade head

db-migrate: ## Create new database migration
	alembic revision --autogenerate -m "$(msg)"

db-upgrade: ## Apply database migrations
	alembic upgrade head

db-downgrade: ## Rollback database migration
	alembic downgrade -1

db-reset: ## Reset database (WARNING: destroys all data)
	rm -f ica_app.db
	alembic upgrade head

# Testing
test: ## Run all tests
	pytest

test-unit: ## Run unit tests only
	pytest tests/unit -v

test-integration: ## Run integration tests only
	pytest tests/integration -v

test-e2e: ## Run end-to-end tests only
	pytest tests/e2e -v

test-security: ## Run security tests only
	pytest tests/security -v

test-ai: ## Run AI/LLM specific tests only
	pytest tests/ai -v

test-coverage: ## Run tests with detailed coverage report
	pytest --cov-report=html --cov-report=term-missing

test-watch: ## Run tests in watch mode
	pytest-watch

# Code Quality
lint: ## Run all linting checks
	ruff check src tests
	black --check src tests
	isort --check-only src tests

format: ## Format code with black and isort
	black src tests
	isort src tests
	ruff check --fix src tests

type-check: ## Run type checking with mypy
	mypy src

security-check: ## Run security checks
	bandit -r src
	safety check

pre-commit: ## Run pre-commit hooks
	pre-commit run --all-files

# Development Server
run: ## Run production server
	uvicorn src.app:app --host 0.0.0.0 --port 8000

dev: ## Run development server with auto-reload
	uvicorn src.app:app --host 0.0.0.0 --port 8000 --reload

dev-debug: ## Run development server with debug logging
	LOG_LEVEL=DEBUG uvicorn src.app:app --host 0.0.0.0 --port 8000 --reload

# Docker
docker-build: ## Build Docker image
	docker build -t ica-app:latest .

docker-run: ## Run Docker container
	docker run -p 8000:8000 --env-file .env ica-app:latest

docker-dev: ## Run Docker container in development mode
	docker run -p 8000:8000 -v $(PWD):/app --env-file .env ica-app:latest

# Documentation
docs-serve: ## Serve documentation locally
	mkdocs serve

docs-build: ## Build documentation
	mkdocs build

docs-deploy: ## Deploy documentation to GitHub Pages
	mkdocs gh-deploy

# Utilities
clean: ## Clean up temporary files and caches
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name ".mypy_cache" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/
	rm -rf htmlcov/
	rm -rf .coverage

install-playwright: ## Install Playwright browsers
	playwright install

check-env: ## Check environment configuration
	@echo "Checking environment configuration..."
	@python -c "import os; print('✓ OPENROUTER_API_KEY:', 'Set' if os.getenv('OPENROUTER_API_KEY') else '✗ Missing')"
	@python -c "import os; print('✓ DATABASE_URL:', os.getenv('DATABASE_URL', 'Using default SQLite'))"
	@echo "Environment check complete"

health-check: ## Check application health
	curl -f http://localhost:8000/health || echo "Application not running"

# CI/CD
ci-install: ## Install dependencies for CI
	pip install -e ".[dev,test]"
	playwright install

ci-test: ## Run tests in CI environment
	pytest --cov=src --cov-report=xml --cov-report=term-missing

ci-lint: ## Run linting in CI environment
	ruff check src tests
	black --check src tests
	isort --check-only src tests
	mypy src

ci-security: ## Run security checks in CI environment
	bandit -r src -f json -o bandit-report.json
	safety check --json --output safety-report.json

# Release
version-patch: ## Bump patch version
	bump2version patch

version-minor: ## Bump minor version
	bump2version minor

version-major: ## Bump major version
	bump2version major

build: ## Build distribution packages
	python -m build

publish-test: ## Publish to test PyPI
	python -m twine upload --repository testpypi dist/*

publish: ## Publish to PyPI
	python -m twine upload dist/*

# Development Workflow
dev-setup: setup install-dev install-playwright db-init ## Complete development setup
	@echo "Development environment ready!"
	@echo "Run 'make dev' to start the development server"

dev-reset: clean db-reset ## Reset development environment
	@echo "Development environment reset"

dev-check: lint type-check test-unit ## Run development checks
	@echo "Development checks passed!"

# Production
prod-check: ## Run production readiness checks
	@echo "Running production readiness checks..."
	make ci-lint
	make ci-test
	make ci-security
	@echo "Production readiness checks complete!"