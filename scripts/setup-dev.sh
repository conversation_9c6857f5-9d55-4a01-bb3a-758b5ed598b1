#!/bin/bash
# Development environment setup script for Industry & Competitor Analysis App

set -e  # Exit on any error

echo "🚀 Setting up Industry & Competitor Analysis App development environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Python 3.11+ is installed
check_python() {
    print_status "Checking Python version..."
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
        if python3 -c 'import sys; exit(0 if sys.version_info >= (3, 11) else 1)'; then
            print_success "Python $PYTHON_VERSION found"
        else
            print_error "Python 3.11+ required, found $PYTHON_VERSION"
            exit 1
        fi
    else
        print_error "Python 3 not found. Please install Python 3.11+"
        exit 1
    fi
}

# Create virtual environment
create_venv() {
    print_status "Creating virtual environment..."
    if [ ! -d ".venv" ]; then
        python3 -m venv .venv
        print_success "Virtual environment created"
    else
        print_warning "Virtual environment already exists"
    fi
}

# Activate virtual environment and install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    source .venv/bin/activate
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install the application with development dependencies
    pip install -e ".[dev,test,docs]"
    
    print_success "Dependencies installed"
}

# Install Playwright browsers
install_playwright() {
    print_status "Installing Playwright browsers..."
    source .venv/bin/activate
    playwright install
    print_success "Playwright browsers installed"
}

# Setup environment file
setup_env() {
    print_status "Setting up environment configuration..."
    if [ ! -f ".env" ]; then
        cp .env.example .env
        print_warning "Created .env file from template. Please update with your API keys!"
        print_warning "You need to set OPENROUTER_API_KEY in .env file"
    else
        print_warning ".env file already exists"
    fi
}

# Initialize database
init_database() {
    print_status "Initializing database..."
    source .venv/bin/activate
    
    # Create database directory if it doesn't exist
    mkdir -p data
    
    # Initialize Alembic if not already done
    if [ ! -d "alembic" ]; then
        alembic init alembic
        print_success "Alembic initialized"
    fi
    
    # Run migrations
    alembic upgrade head
    print_success "Database initialized"
}

# Setup pre-commit hooks
setup_precommit() {
    print_status "Setting up pre-commit hooks..."
    source .venv/bin/activate
    pre-commit install
    print_success "Pre-commit hooks installed"
}

# Run initial tests
run_tests() {
    print_status "Running initial tests..."
    source .venv/bin/activate
    
    # Run linting
    print_status "Running code quality checks..."
    ruff check src tests || print_warning "Linting issues found"
    black --check src tests || print_warning "Code formatting issues found"
    
    # Run unit tests
    print_status "Running unit tests..."
    pytest tests/unit -v || print_warning "Some tests failed"
    
    print_success "Initial tests completed"
}

# Main setup function
main() {
    echo "=========================================="
    echo "Industry & Competitor Analysis App Setup"
    echo "=========================================="
    
    check_python
    create_venv
    install_dependencies
    install_playwright
    setup_env
    init_database
    setup_precommit
    run_tests
    
    echo ""
    echo "=========================================="
    print_success "Development environment setup complete!"
    echo "=========================================="
    echo ""
    echo "Next steps:"
    echo "1. Activate the virtual environment: source .venv/bin/activate"
    echo "2. Update .env file with your OpenAI API key"
    echo "3. Start the development server: make dev"
    echo "4. Visit http://localhost:8000/docs for API documentation"
    echo ""
    echo "Useful commands:"
    echo "  make dev          - Start development server"
    echo "  make test         - Run all tests"
    echo "  make lint         - Run code quality checks"
    echo "  make format       - Format code"
    echo "  make help         - Show all available commands"
    echo ""
}

# Run main function
main "$@"