#!/usr/bin/env python3
"""
Simple script to test database connectivity and basic operations.
"""

import os
import sys
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from storage.database import get_db_session, init_database
from storage.service import AnalysisService
from models import AnalysisIntent, PrimaryGoal

async def test_database():
    """Test database operations."""
    print("🔍 Testing database connectivity...")
    
    try:
        # Initialize database
        await init_database()
        print("✅ Database initialized successfully")
        
        # Test creating an analysis
        sample_intent = AnalysisIntent(
            scope={"primary_goal": PrimaryGoal.competitive_benchmark_feature},
            market={"industry_name": "Test Industry"},
            company_context={"company_name": "Test Company"},
            outputs={"format": "markdown", "depth": "deep_dive"}
        )
        
        # Use database session
        async with get_db_session() as db:
            service = AnalysisService(db)
            
            # Create analysis
            analysis = service.create_analysis(sample_intent, user_id="test-user")
            print(f"✅ Analysis created with ID: {analysis.id}")
            
            # List analyses
            analyses = service.list_analyses(limit=10)
            print(f"✅ Found {len(analyses)} analyses in database")
            
            # Update status
            success = service.update_analysis_status(analysis.id, "processing")
            print(f"✅ Status update: {'success' if success else 'failed'}")
        
        print("🎉 Database test completed successfully!")
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    # Set up environment
    os.environ.setdefault("DATABASE_URL", "sqlite:///./test_ica.db")
    
    # Run test
    success = asyncio.run(test_database())
    sys.exit(0 if success else 1)